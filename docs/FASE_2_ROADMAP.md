# NEUROGLYPH FASE 2 - ROADMAP
*Reasoning Simbolico Reale e Patch Testing*

## 🎯 OBIETTIVI FASE 2 - PRIORITÀ MEDIA

**Durata Stimata**: 2-4 settimane  
**Prerequisito**: Fase 1 completata ✅

### 🧠 2.1 Reasoning Simbolico Reale
**Obiettivo**: Utilizzare effettivamente i 9,236 simboli NEUROGLYPH per inferenze complesse

**Problemi Attuali**:
- Reasoning attuale è logico ma non sfrutta simboli NEUROGLYPH
- Simboli vengono parsati ma non usati per inferenze
- Manca mapping simboli → operatori logici

**Implementazione**:
- Estendere `LogicalOperators` con mapping simboli-predicati
- Creare `SymbolMapper` per conversione simboli → regole semantiche
- Implementare catene di inferenza simboliche (A→B→C via simboli)

**File Chiave**:
- `neuroglyph/cognitive/reasoner/operators.py`
- `neuroglyph/cognitive/reasoner/symbol_mapper.py` (nuovo)
- `neuroglyph/cognitive/reasoning_structures.py`

**Test di Successo**:
- Nuovo test `test_symbolic_reasoning.py` con catena A→B→C via simboli
- Self-check senza errori logici su reasoning simbolico
- Utilizzo di almeno 100 simboli del registry in inferenze reali

### 🔧 2.2 Patch Testing & Validation
**Obiettivo**: Ciclo completo error → patch → test → learn

**Problemi Attuali**:
- Adaptive patcher genera patch ma non le testa
- Manca validazione efficacia delle patch
- Nessun feedback loop per miglioramento

**Implementazione**:
- Estendere `PatchGenerator` con testing automatico
- Integrare sandbox per esecuzione patch candidates
- Implementare metriche `patch_success_rate`
- Feedback loop verso `NGLearner`

**File Chiave**:
- `neuroglyph/cognitive/adaptive_patcher/patch_generator.py`
- `neuroglyph/cognitive/sandbox.py`
- `neuroglyph/cognitive/adaptive_patcher/engine.py`

**Test di Successo**:
- Patch candidate applicata → self-check riduce errori
- Metrica `patch_success_rate` ≥ 70% su suite sintattica
- Nessun warning "No patch candidates generated" con errori indotti

### ⚡ 2.3 Performance Optimization
**Obiettivo**: Gestione file >1000 righe con performance ottimali

**Problemi Attuali**:
- Memory append: 0.52ms (target: <0.5ms)
- Non testato su file grandi
- Database su disco potrebbe essere lento

**Implementazione**:
- Ottimizzazione indici database
- PRAGMA journal_mode=WAL per performance
- Profiling e ottimizzazione bottleneck
- Test su file reali 10k+ record

**File Chiave**:
- `neuroglyph/cognitive/memory.py`
- `neuroglyph/cognitive/memory_models.py`

**Test di Successo**:
- Append ≤ 1ms su SSD
- Retrieve ≤ 5ms per 10k records
- Pipeline completa <100ms per file 1000 righe

### 🔄 2.4 Has_Errors-Driven Patcher Flow
**Obiettivo**: Flusso completo guidato da `ValidationResult.has_errors`

**Problemi Attuali**:
- Patcher non sempre attivato quando necessario
- Manca integrazione fluida con validation

**Implementazione**:
- Refactor `adaptive_patcher/engine.py`
- Trigger automatico su `has_errors = True`
- Prioritizzazione errori per tipo/severità

**File Chiave**:
- `neuroglyph/cognitive/adaptive_patcher/engine.py`
- `neuroglyph/cognitive/integration.py`

**Test di Successo**:
- Nessun warning "No patch candidates" con errori presenti
- Automatic patching su validation errors
- Riduzione errori dopo patching ≥ 50%

## 🛠️ MICRO-ATTIVITÀ IMMEDIATE

### Oggi/Domani
1. **Indici e WAL Database**:
   ```sql
   CREATE INDEX IF NOT EXISTS idx_mem_created ON memory_records(created_at);
   PRAGMA journal_mode=WAL;
   ```

2. **Refactor LogicalOperators**:
   - Mappare simboli (es. ⟲ loop) a predicati semantici
   - Registrare regole simboliche

3. **Bozza Test Patcher**:
   ```python
   def test_patcher_fixes_missing_return():
       code = "def f():\n    pass"
       # ValidationError → patcher aggiunge 'return None'
   ```

### Questa Settimana
4. **Branch feature/symbolic-reasoning**
5. **Suite test patcher completa**
6. **Profiling performance memory**

## 📊 CRITERI DI SUCCESSO FASE 2

| Metrica | Target | Test |
|---------|--------|------|
| Symbolic reasoning | 100+ simboli usati | `test_symbolic_reasoning.py` |
| Patch success rate | ≥ 70% | Suite sintattica |
| Memory performance | ≤ 1ms append, ≤ 5ms retrieve | 10k records |
| Error reduction | ≥ 50% dopo patching | Integration test |
| Pipeline speed | <100ms per 1000 righe | Performance test |

## 🔗 DIPENDENZE

**Fase 2.1** → **Fase 2.2**: Reasoning simbolico necessario per patch intelligenti  
**Fase 2.2** → **Fase 2.4**: Patch testing necessario per flusso completo  
**Fase 2.3**: Indipendente, può essere parallelizzata  

## 📅 TIMELINE STIMATA

```
Settimana 1: 2.1 Reasoning Simbolico (simboli → operatori)
Settimana 2: 2.2 Patch Testing (error → patch → test)  
Settimana 3: 2.3 Performance Optimization
Settimana 4: 2.4 Integration & Testing completo
```

## 🎯 RISULTATO ATTESO

Al termine della Fase 2, NEUROGLYPH avrà:
- **Reasoning simbolico reale** con utilizzo effettivo dei 9,236 simboli
- **Ciclo completo** error → patch → test → learn
- **Performance ottimali** per file grandi
- **Flusso automatico** di correzione errori

Questo trasformerà NEUROGLYPH da "pipeline funzionante" a **"sistema di reasoning simbolico reale"**.

---
*NEUROGLYPH v3.0 ULTRA - Fase 2: Symbolic Reasoning & Intelligent Patching*
