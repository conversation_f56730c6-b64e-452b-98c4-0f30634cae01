"""
NEUROGLYPH Patch Generator
Generatore di patch per auto-correzione
"""

import re
import ast
import time
from typing import List, Dict, Any, Optional, Tuple

from .patcher_structures import (
    ErrorAnalysis, PatchCandidate, PatchType, PatcherConfig,
    ErrorType, LearningPattern
)


class PatchGenerator:
    """
    Generatore di patch per NEUROGLYPH.
    Genera candidati patch basati su analisi errori e pattern appresi.
    """
    
    def __init__(self, config: Optional[PatcherConfig] = None):
        """
        Inizializza il generatore di patch.
        
        Args:
            config: Configurazione patcher
        """
        self.config = config or PatcherConfig()
        self.patch_strategies = self._load_patch_strategies()
        self.learned_patterns = []
        
        print("🔧 PatchGenerator inizializzato")
        print(f"   - Patch strategies: {len(self.patch_strategies)}")
        print(f"   - Max candidates: {self.config.max_patch_candidates}")
        print(f"   - Min confidence: {self.config.min_confidence_threshold}")
    
    def generate_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """
        Genera candidati patch per un errore.
        
        Args:
            error_analysis: Analisi dell'errore
            
        Returns:
            Lista di candidati patch
        """
        candidates = []
        
        # Genera patch basate su pattern dell'errore
        pattern_patches = self._generate_pattern_based_patches(error_analysis)
        candidates.extend(pattern_patches)
        
        # Genera patch basate su tipo errore
        type_patches = self._generate_type_based_patches(error_analysis)
        candidates.extend(type_patches)
        
        # Genera patch basate su pattern appresi
        learned_patches = self._generate_learned_patches(error_analysis)
        candidates.extend(learned_patches)
        
        # Filtra e ordina candidati
        filtered_candidates = self._filter_candidates(candidates)
        sorted_candidates = self._sort_candidates(filtered_candidates)
        
        # Limita numero candidati
        return sorted_candidates[:self.config.max_patch_candidates]

    def generate_and_test_patches(self, error_analysis: ErrorAnalysis, original_code: str,
                                 sandbox, verbose: bool = False) -> List[PatchCandidate]:
        """
        Genera e testa patch candidates nel sandbox (Fase 2.2).

        Args:
            error_analysis: Analisi dell'errore
            original_code: Codice originale
            sandbox: Sandbox per testing
            verbose: Flag per logging dettagliato

        Returns:
            Lista di patch candidates testati con risultati
        """
        if verbose:
            print(f"🔧 Generating and testing patches for error: {error_analysis.error_type}")

        # Genera candidati patch
        candidates = self.generate_patches(error_analysis)

        if not candidates:
            if verbose:
                print("   ⚠️ No patch candidates generated")
            return []

        if verbose:
            print(f"   Generated {len(candidates)} patch candidates")

        # Testa ogni candidato nel sandbox
        tested_candidates = []
        for i, candidate in enumerate(candidates):
            if verbose:
                print(f"   Testing candidate {i+1}/{len(candidates)}: {candidate.patch_id}")

            # Testa nel sandbox
            test_results = sandbox.test_patch_candidate(candidate, original_code, verbose)

            # Aggiorna candidato con risultati
            candidate.test_results = test_results
            candidate.success_probability = test_results.get("overall_score", 0.0)

            # Aggiorna stato basato su test
            if test_results["success"]:
                candidate.status = "tested_success"
            else:
                candidate.status = "tested_failed"

            tested_candidates.append(candidate)

            if verbose:
                status = "✅ SUCCESS" if test_results["success"] else "❌ FAILED"
                print(f"      Result: {status} (score: {test_results.get('overall_score', 0.0):.3f})")

        # Ordina per score di successo
        tested_candidates.sort(key=lambda c: c.success_probability, reverse=True)

        if verbose:
            successful = sum(1 for c in tested_candidates if c.test_results["success"])
            print(f"   📊 Testing completed: {successful}/{len(tested_candidates)} successful")

        return tested_candidates

    def _generate_pattern_based_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch basate su pattern dell'errore."""
        candidates = []
        pattern = error_analysis.error_pattern
        
        if pattern in self.patch_strategies:
            strategy = self.patch_strategies[pattern]
            
            for patch_template in strategy['patches']:
                candidate = self._create_patch_from_template(
                    error_analysis, patch_template, strategy
                )
                if candidate:
                    candidates.append(candidate)
        
        return candidates
    
    def _generate_type_based_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch basate su tipo errore."""
        candidates = []
        error_type = error_analysis.error_type
        
        if error_type == ErrorType.SYNTAX_ERROR:
            candidates.extend(self._generate_syntax_patches(error_analysis))
        elif error_type == ErrorType.RUNTIME_ERROR:
            candidates.extend(self._generate_runtime_patches(error_analysis))
        elif error_type == ErrorType.LOGIC_ERROR:
            candidates.extend(self._generate_logic_patches(error_analysis))
        elif error_type == ErrorType.PERFORMANCE_ERROR:
            candidates.extend(self._generate_performance_patches(error_analysis))
        elif error_type == ErrorType.SECURITY_ERROR:
            candidates.extend(self._generate_security_patches(error_analysis))
        
        return candidates
    
    def _generate_syntax_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori di sintassi."""
        candidates = []
        message = error_analysis.error_message
        
        # Parentesi mancanti
        if "unexpected EOF" in message or "expected" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.CODE_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Add missing parentheses or brackets",
                patch_strategy="syntax_completion",
                confidence=0.7,
                risk_level="low"
            )
            candidates.append(candidate)
        
        # Indentazione
        if "indent" in message:
            candidate = PatchCandidate(
                patch_type=PatchType.CODE_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Fix indentation",
                patch_strategy="indentation_fix",
                confidence=0.8,
                risk_level="low"
            )
            candidates.append(candidate)
        
        # Due punti mancanti
        if "invalid syntax" in message and any(keyword in error_analysis.error_context 
                                              for keyword in ["if", "for", "while", "def", "class"]):
            candidate = PatchCandidate(
                patch_type=PatchType.CODE_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Add missing colon",
                patch_strategy="colon_addition",
                confidence=0.9,
                risk_level="low"
            )
            candidates.append(candidate)
        
        return candidates
    
    def _generate_runtime_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori di runtime."""
        candidates = []
        message = error_analysis.error_message
        
        # Variabile non definita
        if "is not defined" in message:
            var_match = re.search(r"name '(\w+)' is not defined", message)
            if var_match:
                var_name = var_match.group(1)
                
                # Patch: inizializza variabile
                candidate = PatchCandidate(
                    patch_type=PatchType.CODE_FIX,
                    target_error_id=error_analysis.error_id,
                    patch_description=f"Initialize variable '{var_name}'",
                    patch_strategy="variable_initialization",
                    confidence=0.6,
                    risk_level="medium"
                )
                candidate.metadata = {"variable_name": var_name}
                candidates.append(candidate)
        
        # Attributo mancante
        if "has no attribute" in message:
            attr_match = re.search(r"'(\w+)' object has no attribute '(\w+)'", message)
            if attr_match:
                obj_type, attr_name = attr_match.groups()
                
                candidate = PatchCandidate(
                    patch_type=PatchType.CODE_FIX,
                    target_error_id=error_analysis.error_id,
                    patch_description=f"Add attribute check for '{attr_name}'",
                    patch_strategy="attribute_check",
                    confidence=0.7,
                    risk_level="low"
                )
                candidate.metadata = {"object_type": obj_type, "attribute_name": attr_name}
                candidates.append(candidate)
        
        # Import mancante
        if "No module named" in message:
            module_match = re.search(r"No module named '(\w+)'", message)
            if module_match:
                module_name = module_match.group(1)
                
                candidate = PatchCandidate(
                    patch_type=PatchType.CODE_FIX,
                    target_error_id=error_analysis.error_id,
                    patch_description=f"Add import for module '{module_name}'",
                    patch_strategy="import_addition",
                    confidence=0.8,
                    risk_level="low"
                )
                candidate.metadata = {"module_name": module_name}
                candidates.append(candidate)
        
        return candidates
    
    def _generate_logic_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori logici."""
        candidates = []
        
        if "contradiction" in error_analysis.error_message.lower():
            candidate = PatchCandidate(
                patch_type=PatchType.LOGIC_FIX,
                target_error_id=error_analysis.error_id,
                patch_description="Resolve logical contradiction",
                patch_strategy="contradiction_resolution",
                confidence=0.5,
                risk_level="high"
            )
            candidates.append(candidate)
        
        if "low quality reasoning" in error_analysis.error_message.lower():
            candidate = PatchCandidate(
                patch_type=PatchType.ALGORITHM_REPLACEMENT,
                target_error_id=error_analysis.error_id,
                patch_description="Improve reasoning quality",
                patch_strategy="reasoning_enhancement",
                confidence=0.4,
                risk_level="medium"
            )
            candidates.append(candidate)
        
        return candidates
    
    def _generate_performance_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori di performance."""
        candidates = []
        
        if "timeout" in error_analysis.error_message.lower():
            # Patch: ottimizzazione algoritmo
            candidate = PatchCandidate(
                patch_type=PatchType.PERFORMANCE_OPTIMIZATION,
                target_error_id=error_analysis.error_id,
                patch_description="Optimize algorithm for better performance",
                patch_strategy="algorithm_optimization",
                confidence=0.6,
                risk_level="medium"
            )
            candidates.append(candidate)
            
            # Patch: aumento timeout
            candidate = PatchCandidate(
                patch_type=PatchType.PARAMETER_ADJUSTMENT,
                target_error_id=error_analysis.error_id,
                patch_description="Increase timeout limit",
                patch_strategy="timeout_adjustment",
                confidence=0.8,
                risk_level="low"
            )
            candidates.append(candidate)
        
        return candidates
    
    def _generate_security_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch per errori di sicurezza."""
        candidates = []
        
        candidate = PatchCandidate(
            patch_type=PatchType.SECURITY_HARDENING,
            target_error_id=error_analysis.error_id,
            patch_description="Apply security hardening",
            patch_strategy="security_enhancement",
            confidence=0.9,
            risk_level="low"
        )
        candidates.append(candidate)
        
        return candidates
    
    def _generate_learned_patches(self, error_analysis: ErrorAnalysis) -> List[PatchCandidate]:
        """Genera patch basate su pattern appresi."""
        candidates = []
        
        # Cerca pattern simili nei pattern appresi
        for pattern in self.learned_patterns:
            if self._pattern_matches(pattern, error_analysis):
                candidate = self._create_patch_from_learned_pattern(
                    error_analysis, pattern
                )
                if candidate:
                    candidates.append(candidate)
        
        return candidates
    
    def _create_patch_from_template(self, error_analysis: ErrorAnalysis, 
                                   template: Dict[str, Any], 
                                   strategy: Dict[str, Any]) -> Optional[PatchCandidate]:
        """Crea patch da template."""
        candidate = PatchCandidate(
            patch_type=PatchType(template.get('type', 'code_fix')),
            target_error_id=error_analysis.error_id,
            patch_description=template.get('description', ''),
            patch_strategy=template.get('strategy', ''),
            confidence=template.get('confidence', 0.5),
            risk_level=template.get('risk_level', 'medium')
        )
        
        # Personalizza patch basata su contesto errore
        candidate = self._customize_patch(candidate, error_analysis)
        
        return candidate
    
    def _create_patch_from_learned_pattern(self, error_analysis: ErrorAnalysis,
                                          pattern: LearningPattern) -> Optional[PatchCandidate]:
        """Crea patch da pattern appreso."""
        candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            target_error_id=error_analysis.error_id,
            patch_description=f"Apply learned pattern: {pattern.pattern_name}",
            patch_strategy="learned_pattern",
            confidence=pattern.effectiveness_score,
            risk_level="medium"
        )
        
        candidate.metadata = {
            "learned_pattern_id": pattern.pattern_id,
            "pattern_effectiveness": pattern.effectiveness_score
        }
        
        return candidate
    
    def _customize_patch(self, candidate: PatchCandidate, 
                        error_analysis: ErrorAnalysis) -> PatchCandidate:
        """Personalizza patch basata su contesto errore."""
        # Aggiusta confidence basata su severità errore
        if error_analysis.severity == "critical":
            candidate.confidence *= 1.2  # Aumenta confidence per errori critici
        elif error_analysis.severity == "low":
            candidate.confidence *= 0.8  # Diminuisci per errori minori
        
        # Aggiusta risk level
        if error_analysis.impact_score > 0.8:
            if candidate.risk_level == "low":
                candidate.risk_level = "medium"
            elif candidate.risk_level == "medium":
                candidate.risk_level = "high"
        
        # Clamp confidence
        candidate.confidence = max(0.0, min(1.0, candidate.confidence))
        
        return candidate
    
    def _filter_candidates(self, candidates: List[PatchCandidate]) -> List[PatchCandidate]:
        """Filtra candidati patch."""
        filtered = []
        
        for candidate in candidates:
            # Filtra per confidence minima
            if candidate.confidence < self.config.min_confidence_threshold:
                continue
            
            # Filtra per risk level massimo
            risk_levels = ["low", "medium", "high", "critical"]
            max_risk_index = risk_levels.index(self.config.max_risk_level)
            candidate_risk_index = risk_levels.index(candidate.risk_level)
            
            if candidate_risk_index > max_risk_index:
                continue
            
            filtered.append(candidate)
        
        return filtered
    
    def _sort_candidates(self, candidates: List[PatchCandidate]) -> List[PatchCandidate]:
        """Ordina candidati per qualità."""
        def patch_score(candidate: PatchCandidate) -> float:
            # Score basato su confidence e risk
            risk_penalty = {
                "low": 0.0,
                "medium": 0.1,
                "high": 0.3,
                "critical": 0.5
            }
            
            penalty = risk_penalty.get(candidate.risk_level, 0.2)
            return candidate.confidence - penalty
        
        return sorted(candidates, key=patch_score, reverse=True)
    
    def _pattern_matches(self, pattern: LearningPattern, 
                        error_analysis: ErrorAnalysis) -> bool:
        """Verifica se un pattern appreso corrisponde all'errore."""
        # Matching semplice basato su pattern dell'errore
        return pattern.error_pattern == error_analysis.error_pattern
    
    def _load_patch_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Carica strategie di patch."""
        return {
            "undefined_variable": {
                "patches": [
                    {
                        "type": "code_fix",
                        "description": "Initialize variable with default value",
                        "strategy": "variable_init",
                        "confidence": 0.7,
                        "risk_level": "medium"
                    }
                ]
            },
            "missing_attribute": {
                "patches": [
                    {
                        "type": "code_fix", 
                        "description": "Add attribute check",
                        "strategy": "attribute_guard",
                        "confidence": 0.8,
                        "risk_level": "low"
                    }
                ]
            },
            "syntax_invalid": {
                "patches": [
                    {
                        "type": "code_fix",
                        "description": "Fix syntax error",
                        "strategy": "syntax_repair",
                        "confidence": 0.9,
                        "risk_level": "low"
                    }
                ]
            }
        }
    
    def add_learned_pattern(self, pattern: LearningPattern):
        """Aggiunge pattern appreso."""
        self.learned_patterns.append(pattern)
    
    def get_generation_statistics(self) -> Dict[str, Any]:
        """Ottiene statistiche di generazione."""
        return {
            "strategies_available": len(self.patch_strategies),
            "learned_patterns": len(self.learned_patterns),
            "max_candidates": self.config.max_patch_candidates,
            "min_confidence": self.config.min_confidence_threshold
        }
