"""
NEUROGLYPH Adaptive Patcher Structures
Strutture dati per auto-correzione e adaptive learning
"""

import time
from enum import Enum
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timezone


class ErrorType(Enum):
    """Tipi di errori rilevabili."""
    SYNTAX_ERROR = "syntax_error"           # Errori di sintassi
    RUNTIME_ERROR = "runtime_error"         # Errori di runtime
    LOGIC_ERROR = "logic_error"             # Errori logici
    SEMANTIC_ERROR = "semantic_error"       # Errori semantici
    PERFORMANCE_ERROR = "performance_error" # Errori di performance
    SECURITY_ERROR = "security_error"       # Errori di sicurezza
    VALIDATION_ERROR = "validation_error"   # Errori di validazione
    REASONING_ERROR = "reasoning_error"     # Errori di reasoning


class PatchType(Enum):
    """Tipi di patch applicabili."""
    CODE_FIX = "code_fix"                   # Correzione codice
    LOGIC_FIX = "logic_fix"                 # Correzione logica
    PARAMETER_ADJUSTMENT = "parameter_adjustment"  # Aggiustamento parametri
    ALGORITHM_REPLACEMENT = "algorithm_replacement"  # Sostituzione algoritmo
    VALIDATION_ENHANCEMENT = "validation_enhancement"  # Miglioramento validazione
    SECURITY_HARDENING = "security_hardening"  # Hardening sicurezza
    PERFORMANCE_OPTIMIZATION = "performance_optimization"  # Ottimizzazione performance


class PatchStatus(Enum):
    """Stati di una patch."""
    GENERATED = "generated"                 # Generata
    VALIDATED = "validated"                 # Validata
    APPLIED = "applied"                     # Applicata
    TESTED = "tested"                       # Testata
    SUCCESSFUL = "successful"               # Successo
    FAILED = "failed"                       # Fallita
    REVERTED = "reverted"                   # Revertita


class LearningMode(Enum):
    """Modalità di apprendimento."""
    CONSERVATIVE = "conservative"           # Conservativo
    BALANCED = "balanced"                   # Bilanciato
    AGGRESSIVE = "aggressive"               # Aggressivo
    EXPERIMENTAL = "experimental"           # Sperimentale


@dataclass
class ErrorAnalysis:
    """Analisi di un errore."""
    
    # Identificazione
    error_id: str = field(default_factory=lambda: str(time.time()))
    error_type: ErrorType = ErrorType.RUNTIME_ERROR
    
    # Descrizione
    error_message: str = ""
    error_location: str = ""                # File, linea, funzione
    error_context: str = ""                 # Contesto dell'errore
    
    # Traceback
    traceback: str = ""
    stack_trace: List[str] = field(default_factory=list)
    
    # Analisi
    root_cause: str = ""                    # Causa principale
    contributing_factors: List[str] = field(default_factory=list)
    error_pattern: str = ""                 # Pattern dell'errore
    
    # Frequenza
    occurrence_count: int = 1
    first_seen: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_seen: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Impatto
    severity: str = "medium"                # low, medium, high, critical
    impact_score: float = 0.5               # 0.0-1.0
    
    # Contesto esecuzione
    execution_context: Dict[str, Any] = field(default_factory=dict)
    input_data: Dict[str, Any] = field(default_factory=dict)
    
    # Metadati
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PatchCandidate:
    """Candidato patch per correzione."""
    
    # Identificazione
    patch_id: str = field(default_factory=lambda: str(time.time()))
    patch_type: PatchType = PatchType.CODE_FIX
    
    # Target
    target_error_id: str = ""               # ID errore da correggere
    target_location: str = ""               # Dove applicare la patch
    
    # Patch content
    original_code: str = ""                 # Codice originale
    patched_code: str = ""                  # Codice corretto
    patch_description: str = ""             # Descrizione della patch
    suggested_fix: str = ""                 # Fix suggerito (Fase 2.2)
    
    # Strategia
    patch_strategy: str = ""                # Strategia utilizzata
    confidence: float = 0.5                 # Confidenza nella patch
    risk_level: str = "medium"              # low, medium, high
    
    # Validazione
    is_validated: bool = False
    validation_results: Dict[str, Any] = field(default_factory=dict)
    
    # Test
    test_cases: List[Dict[str, Any]] = field(default_factory=list)
    test_results: List[Dict[str, Any]] = field(default_factory=list)
    
    # Applicazione
    status: PatchStatus = PatchStatus.GENERATED
    applied_at: Optional[datetime] = None
    reverted_at: Optional[datetime] = None
    
    # Performance
    performance_impact: Dict[str, float] = field(default_factory=dict)
    success_rate: float = 0.0               # Tasso di successo
    success_probability: float = 0.0        # Probabilità di successo (Fase 2.2)
    
    # Metadati
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class LearningPattern:
    """Pattern di apprendimento rilevato."""
    
    # Identificazione
    pattern_id: str = field(default_factory=lambda: str(time.time()))
    pattern_name: str = ""
    
    # Pattern
    error_pattern: str = ""                 # Pattern dell'errore
    solution_pattern: str = ""              # Pattern della soluzione
    context_pattern: str = ""               # Pattern del contesto
    
    # Frequenza
    occurrence_count: int = 1
    success_count: int = 0
    failure_count: int = 0
    
    # Efficacia
    effectiveness_score: float = 0.0        # 0.0-1.0
    confidence_score: float = 0.0           # 0.0-1.0
    
    # Applicabilità
    applicable_contexts: List[str] = field(default_factory=list)
    exclusion_contexts: List[str] = field(default_factory=list)
    
    # Evoluzione
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    version: int = 1
    
    # Metadati
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AdaptiveLearningState:
    """Stato dell'apprendimento adattivo."""
    
    # Configurazione
    learning_mode: LearningMode = LearningMode.BALANCED
    learning_rate: float = 0.1              # Tasso di apprendimento
    
    # Statistiche errori
    total_errors_seen: int = 0
    total_errors_fixed: int = 0
    total_patches_generated: int = 0
    total_patches_successful: int = 0
    
    # Pattern
    learned_patterns: List[LearningPattern] = field(default_factory=list)
    active_patterns: List[str] = field(default_factory=list)  # IDs pattern attivi
    
    # Performance
    overall_success_rate: float = 0.0
    recent_success_rate: float = 0.0        # Ultimi N tentativi
    improvement_trend: float = 0.0          # Trend di miglioramento
    
    # Adattamento
    adaptation_threshold: float = 0.8       # Soglia per adattamento
    confidence_threshold: float = 0.7       # Soglia confidenza
    
    # Temporali
    learning_started: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_adaptation: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Metadati
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def update_success_rate(self):
        """Aggiorna tasso di successo."""
        if self.total_patches_generated > 0:
            self.overall_success_rate = self.total_patches_successful / self.total_patches_generated
    
    def should_adapt(self) -> bool:
        """Verifica se dovrebbe adattarsi."""
        return (self.recent_success_rate < self.adaptation_threshold and
                self.total_patches_generated >= 10)


@dataclass
class PatcherConfig:
    """Configurazione dell'adaptive patcher."""
    
    # Apprendimento
    learning_mode: LearningMode = LearningMode.BALANCED
    learning_rate: float = 0.1
    adaptation_frequency: int = 10          # Ogni N patch
    
    # Generazione patch
    max_patch_candidates: int = 5           # Max candidati per errore
    min_confidence_threshold: float = 0.3   # Confidenza minima
    max_risk_level: str = "medium"          # Rischio massimo accettabile
    
    # Validazione
    enable_patch_validation: bool = True
    enable_test_generation: bool = True
    validation_timeout: float = 30.0        # secondi
    
    # Pattern learning
    min_pattern_occurrences: int = 3        # Min occorrenze per pattern
    pattern_decay_rate: float = 0.95        # Decadimento pattern
    max_patterns_stored: int = 1000         # Max pattern memorizzati
    
    # Sicurezza
    enable_safety_checks: bool = True
    allow_risky_patches: bool = False
    require_human_approval: bool = False    # Per patch ad alto rischio
    
    # Performance
    max_patch_generation_time: float = 10.0  # secondi
    enable_performance_monitoring: bool = True
    
    # Logging
    enable_detailed_logging: bool = True
    log_all_attempts: bool = False          # Log anche tentativi falliti
    
    # Persistenza
    save_learning_state: bool = True
    learning_state_file: str = "adaptive_learning_state.json"


@dataclass
class PatchResult:
    """Risultato dell'applicazione di una patch."""
    
    # Identificazione
    patch_id: str = ""
    error_id: str = ""
    
    # Risultato
    success: bool = False
    status: PatchStatus = PatchStatus.FAILED
    
    # Output
    output: Any = None
    error_message: str = ""
    
    # Performance
    execution_time: float = 0.0
    memory_usage: float = 0.0
    
    # Validazione
    validation_passed: bool = False
    test_results: List[Dict[str, Any]] = field(default_factory=list)
    
    # Impatto
    performance_improvement: float = 0.0    # % miglioramento
    quality_improvement: float = 0.0        # % miglioramento qualità
    
    # Temporali
    applied_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Metadati
    metadata: Dict[str, Any] = field(default_factory=dict)
