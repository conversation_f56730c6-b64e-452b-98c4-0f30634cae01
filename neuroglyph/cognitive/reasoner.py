"""
NEUROGLYPH Reasoner
Motore di reasoning simbolico Tree-of-Thought per NEUROGLYPH
"""

import time
import random
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass

from .reasoning_structures import (
    LogicalFact, ReasoningStep, ReasoningPath, ReasoningGraph,
    LogicalOperator, ReasoningType, FactType
)
from .logical_operators import LogicalOperators
from .data_structures import MemoryContext, ParsedPrompt


class PatternMatcher:
    """Matcher per pattern analogici e causali."""
    
    def __init__(self):
        self.patterns = self._load_patterns()
    
    def find_analogies(self, facts: List[LogicalFact], 
                      memory_context: MemoryContext) -> List[ReasoningStep]:
        """Trova analogie tra fatti correnti e memoria."""
        steps = []
        
        # Cerca pattern simili negli esempi
        for example in memory_context.examples:
            for fact in facts:
                similarity = self._calculate_similarity(fact.statement, example.get('prompt', ''))
                if similarity > 0.7:  # Soglia similarità
                    # Crea reasoning analogico
                    analogy_fact = LogicalFact(
                        statement=f"By analogy with '{example.get('prompt', '')}', {fact.statement}",
                        predicate="analogy",
                        arguments=[fact.statement, example.get('prompt', '')],
                        fact_type=FactType.HYPOTHESIS,
                        confidence=similarity * fact.confidence,
                        derived_from=[fact.fact_id],
                        derivation_rule="analogical_reasoning"
                    )
                    
                    step = ReasoningStep(
                        operation=LogicalOperator.ASSUME,
                        reasoning_type=ReasoningType.ANALOGICAL,
                        rule_name="analogical_reasoning",
                        input_facts=[fact],
                        output_facts=[analogy_fact],
                        reasoning_text=f"Analogical reasoning from example: {example.get('prompt', '')}",
                        confidence=similarity
                    )
                    
                    steps.append(step)
        
        return steps
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calcola similarità tra due testi."""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0
    
    def _load_patterns(self) -> Dict[str, Any]:
        """Carica pattern per analogie."""
        return {
            "causal": ["because", "since", "due to", "caused by"],
            "temporal": ["before", "after", "then", "when", "while"],
            "spatial": ["above", "below", "near", "far", "inside"],
            "similarity": ["like", "similar to", "as", "such as"]
        }


class ContradictionChecker:
    """Checker per contraddizioni logiche."""
    
    def __init__(self):
        self.contradiction_patterns = self._load_contradiction_patterns()
    
    def check_contradictions(self, facts: List[LogicalFact]) -> List[Tuple[LogicalFact, LogicalFact]]:
        """Verifica contraddizioni tra fatti."""
        contradictions = []
        
        for i, fact1 in enumerate(facts):
            for fact2 in facts[i+1:]:
                if self._are_contradictory(fact1, fact2):
                    contradictions.append((fact1, fact2))
        
        return contradictions
    
    def _are_contradictory(self, fact1: LogicalFact, fact2: LogicalFact) -> bool:
        """Verifica se due fatti sono contraddittori."""
        # Contraddizione diretta (A vs ¬A)
        if fact1.contradicts(fact2):
            return True
        
        # Contraddizioni semantiche
        for pattern in self.contradiction_patterns:
            if (pattern["positive"] in fact1.statement.lower() and 
                pattern["negative"] in fact2.statement.lower()):
                return True
            if (pattern["negative"] in fact1.statement.lower() and 
                pattern["positive"] in fact2.statement.lower()):
                return True
        
        return False
    
    def _load_contradiction_patterns(self) -> List[Dict[str, str]]:
        """Carica pattern di contraddizione semantica."""
        return [
            {"positive": "is safe", "negative": "is dangerous"},
            {"positive": "is working", "negative": "is broken"},
            {"positive": "is true", "negative": "is false"},
            {"positive": "exists", "negative": "does not exist"},
            {"positive": "is possible", "negative": "is impossible"},
            {"positive": "is valid", "negative": "is invalid"},
        ]


class NGReasoner:
    """
    Reasoning Engine simbolico per NEUROGLYPH.
    Implementa Tree-of-Thought multi-hop reasoning.
    """
    
    def __init__(self, max_depth: int = 8, max_paths: int = 5):
        """
        Inizializza il reasoner.
        
        Args:
            max_depth: Profondità massima reasoning
            max_paths: Numero massimo percorsi paralleli
        """
        self.max_depth = max_depth
        self.max_paths = max_paths
        
        # Componenti
        self.operators = LogicalOperators()
        self.pattern_matcher = PatternMatcher()
        self.contradiction_checker = ContradictionChecker()

        # Symbolic reasoning (Fase 2.1)
        from .symbolic_reasoning.symbol_mapper import SymbolMapper
        self.symbol_mapper = SymbolMapper()
        
        print(f"🧠 NGReasoner inizializzato")
        print(f"   - Max depth: {self.max_depth}")
        print(f"   - Max paths: {self.max_paths}")
    
    def reason(self, memory_context: MemoryContext, 
              parsed_prompt: ParsedPrompt) -> ReasoningGraph:
        """
        Esegue reasoning simbolico multi-hop.
        
        Args:
            memory_context: Contesto di memoria
            parsed_prompt: Prompt parsato
            
        Returns:
            Grafo di reasoning con percorsi e conclusioni
        """
        start_time = time.time()
        
        # Crea grafo di reasoning
        graph = ReasoningGraph(
            max_depth=self.max_depth,
            max_paths=self.max_paths
        )
        
        # Estrai fatti iniziali
        initial_facts = self._extract_initial_facts(memory_context, parsed_prompt)
        graph.add_initial_facts(initial_facts)
        
        print(f"🧠 Starting reasoning with {len(initial_facts)} initial facts")
        
        # Tree-of-Thought reasoning
        for depth in range(self.max_depth):
            print(f"   🔍 Reasoning depth {depth + 1}/{self.max_depth}")
            
            active_paths = graph.get_active_paths()
            if not active_paths:
                break
            
            new_paths = []
            
            for path in active_paths:
                # Applica operatori logici
                logical_steps = self.operators.deduce(path.current_facts)
                
                # Applica reasoning analogico
                analogical_steps = self.pattern_matcher.find_analogies(
                    path.current_facts, memory_context
                )
                
                # Combina tutti gli steps
                all_steps = logical_steps + analogical_steps
                
                # Filtra steps validi
                valid_steps = [step for step in all_steps if step.confidence >= 0.1]
                
                if valid_steps:
                    # Crea nuovi percorsi per ogni step promettente
                    for step in valid_steps[:3]:  # Limita branching
                        new_path = self._create_branch_path(path, step)
                        new_paths.append(new_path)
                else:
                    # Nessun nuovo step, marca percorso come completo
                    path.is_complete = True
                    path.is_active = False
            
            # Aggiunge nuovi percorsi
            for new_path in new_paths:
                graph.add_path(new_path)
            
            # Pota percorsi con score basso
            graph.prune_paths()
            
            # Rileva contraddizioni
            graph.detect_contradictions()
            
            # Verifica condizioni di stop
            if self._should_stop_reasoning(graph):
                break
        
        # Finalizza reasoning
        graph.is_complete = True
        best_path = graph.get_best_path()
        
        reasoning_time = time.time() - start_time
        
        print(f"🧠 Reasoning completed in {reasoning_time:.3f}s")
        print(f"   - Total paths: {len(graph.paths)}")
        print(f"   - Total facts: {graph.total_facts}")
        print(f"   - Total steps: {graph.total_steps}")
        print(f"   - Max depth reached: {graph.max_depth_reached}")
        print(f"   - Contradictions: {len(graph.contradictions)}")
        if best_path:
            print(f"   - Best path score: {best_path.overall_score:.3f}")
        
        return graph
    
    def _extract_initial_facts(self, memory_context: MemoryContext, 
                              parsed_prompt: ParsedPrompt) -> List[LogicalFact]:
        """Estrae fatti iniziali da memoria e prompt."""
        facts = []
        
        # Fatti dal prompt
        prompt_fact = LogicalFact(
            statement=parsed_prompt.original_text,
            predicate="user_request",
            arguments=[parsed_prompt.original_text],
            fact_type=FactType.PREMISE,
            confidence=1.0,
            truth_value=True
        )
        facts.append(prompt_fact)
        
        # Fatti dagli intenti
        for intent in parsed_prompt.intents:
            intent_fact = LogicalFact(
                statement=f"user_intent({intent})",
                predicate="user_intent",
                arguments=[intent],
                fact_type=FactType.PREMISE,
                confidence=0.8
            )
            facts.append(intent_fact)
        
        # Fatti dalla memoria (esempi)
        for example in memory_context.examples[:3]:  # Limita a 3 esempi
            example_fact = LogicalFact(
                statement=example.get('prompt', ''),
                predicate="example",
                arguments=[example.get('domain', 'general')],
                fact_type=FactType.OBSERVATION,
                confidence=example.get('relevance', 0.5)
            )
            facts.append(example_fact)
        
        # Fatti dagli errori (per evitarli)
        for error in memory_context.errors[:2]:  # Limita a 2 errori
            error_fact = LogicalFact(
                statement=f"avoid_error({error.get('prompt', '')})",
                predicate="avoid_error",
                arguments=[error.get('prompt', '')],
                fact_type=FactType.ASSUMPTION,
                confidence=0.7
            )
            facts.append(error_fact)

        # 🧠 FASE 2.1: Fatti simbolici da NEUROGLYPH symbols
        if parsed_prompt.symbols:
            symbolic_facts = self._extract_symbolic_facts(parsed_prompt.symbols, parsed_prompt)
            facts.extend(symbolic_facts)
            print(f"   🔮 Symbolic facts extracted: {len(symbolic_facts)}")

        return facts

    def _extract_symbolic_facts(self, symbols: List[str], parsed_prompt: ParsedPrompt) -> List[LogicalFact]:
        """
        Estrae fatti logici dai simboli NEUROGLYPH (Fase 2.1).

        Args:
            symbols: Lista simboli NEUROGLYPH
            parsed_prompt: Prompt parsato per contesto

        Returns:
            Lista di fatti logici derivati dai simboli
        """
        symbolic_facts = []

        # Usa SymbolMapper per creare fatti logici
        context = {
            "prompt": parsed_prompt.original_text,
            "tokens": parsed_prompt.tokens,
            "intents": parsed_prompt.intents
        }

        # Crea fatti base dai simboli
        symbol_fact_data = self.symbol_mapper.create_logical_facts(symbols, context)

        for fact_data in symbol_fact_data:
            if "predicate" in fact_data:
                # Crea LogicalFact da symbol mapping
                logical_fact = LogicalFact(
                    statement=f"{fact_data['predicate']}({fact_data.get('symbol', 'unknown')})",
                    predicate=fact_data['predicate'],
                    arguments=[fact_data.get('symbol', 'unknown')],
                    fact_type=FactType.PREMISE,
                    confidence=fact_data.get('confidence', 0.8),
                    metadata={
                        "symbol": fact_data.get('symbol'),
                        "category": fact_data.get('category'),
                        "source": "symbol_mapper"
                    }
                )
                symbolic_facts.append(logical_fact)

            elif "rule" in fact_data:
                # Crea fatti derivati dalle regole simboliche
                rule_fact = LogicalFact(
                    statement=fact_data['rule'],
                    predicate=f"rule_{fact_data.get('symbol', 'unknown')}",
                    arguments=[fact_data['rule']],
                    fact_type=FactType.DERIVED,  # Importante: marca come DERIVED
                    confidence=fact_data.get('confidence', 0.6),
                    metadata={
                        "symbol": fact_data.get('symbol'),
                        "rule_type": "symbolic",
                        "source": "symbol_mapper"
                    }
                )
                symbolic_facts.append(rule_fact)

        return symbolic_facts

    def _create_branch_path(self, parent_path: ReasoningPath,
                           step: ReasoningStep) -> ReasoningPath:
        """Crea nuovo percorso branch da un percorso padre."""
        new_path = ReasoningPath(
            parent_path_id=parent_path.path_id
        )
        
        # Copia fatti correnti dal padre
        new_path.current_facts = parent_path.current_facts.copy()
        
        # Aggiunge step
        new_path.add_step(step)
        
        return new_path
    
    def _should_stop_reasoning(self, graph: ReasoningGraph) -> bool:
        """Verifica se il reasoning dovrebbe fermarsi."""
        # Stop se nessun percorso attivo
        if not graph.get_active_paths():
            return True
        
        # Stop se troppi percorsi con contraddizioni
        contradictory_paths = sum(1 for path in graph.paths if path.has_contradiction)
        if contradictory_paths > len(graph.paths) * 0.8:
            return True
        
        # Stop se il miglior percorso ha score molto alto
        best_path = graph.get_best_path()
        if best_path and best_path.overall_score > 0.9:
            return True
        
        return False
    
    def get_conclusions(self, graph: ReasoningGraph) -> List[LogicalFact]:
        """Estrae conclusioni dal grafo di reasoning."""
        best_path = graph.get_best_path()
        if not best_path:
            return []
        
        # Fatti derivati con alta confidenza
        conclusions = []
        for fact in best_path.current_facts:
            if (fact.fact_type in [FactType.DERIVED, FactType.CONCLUSION] and
                fact.confidence >= 0.5 and
                fact.truth_value != False):
                conclusions.append(fact)
        
        return conclusions
    
    def explain_reasoning(self, graph: ReasoningGraph) -> str:
        """Genera spiegazione del reasoning."""
        best_path = graph.get_best_path()
        if not best_path:
            return "No reasoning path found."
        
        explanation = []
        explanation.append(f"Reasoning completed with {len(best_path.steps)} steps:")
        
        for i, step in enumerate(best_path.steps, 1):
            explanation.append(f"{i}. {step.reasoning_text} (confidence: {step.confidence:.2f})")
        
        conclusions = self.get_conclusions(graph)
        if conclusions:
            explanation.append(f"\nConclusions:")
            for conclusion in conclusions:
                explanation.append(f"- {conclusion.statement} (confidence: {conclusion.confidence:.2f})")
        
        if graph.contradictions:
            explanation.append(f"\nContradictions detected: {len(graph.contradictions)}")
        
        return "\n".join(explanation)
