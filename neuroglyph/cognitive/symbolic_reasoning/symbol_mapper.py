"""
NEUROGLYPH Symbol Mapper
Mapping dei simboli NEUROGLYPH a operatori logici e predicati semantici
"""

import json
import os
from typing import Dict, List, Set, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum


class SymbolCategory(Enum):
    """Categorie di simboli NEUROGLYPH."""
    CONTROL_FLOW = "control_flow"
    DATA_STRUCTURE = "data_structure"
    LOGICAL_OPERATOR = "logical_operator"
    MATHEMATICAL = "mathematical"
    SEMANTIC = "semantic"
    TEMPORAL = "temporal"
    RELATIONAL = "relational"
    UNKNOWN = "unknown"


@dataclass
class SymbolMapping:
    """Mapping di un simbolo a predicati logici."""
    symbol: str
    category: SymbolCategory
    semantic_predicate: str
    logical_rules: List[str]
    inference_patterns: List[str]
    confidence: float = 1.0
    usage_count: int = 0


class SymbolMapper:
    """
    Mapper per convertire simboli NEUROGLYPH in operatori logici.
    
    Trasforma simboli Unicode in predicati semantici utilizzabili
    dal reasoning engine per inferenze simboliche reali.
    """
    
    def __init__(self, registry_path: Optional[str] = None):
        """
        Inizializza il symbol mapper.
        
        Args:
            registry_path: Percorso al registry NEUROGLYPH
        """
        self.registry_path = registry_path or self._get_default_registry_path()
        self.symbol_mappings: Dict[str, SymbolMapping] = {}
        self.category_index: Dict[SymbolCategory, Set[str]] = {}
        self.predicate_index: Dict[str, Set[str]] = {}
        
        # Lazy loading - carica solo quando necessario
        self.registry = None
        self._registry_loaded = False

        # Crea mappings base (senza registry)
        self._create_base_mappings()
        
        print(f"🧠 SymbolMapper inizializzato")
        print(f"   - Simboli mappati: {len(self.symbol_mappings)}")
        print(f"   - Categorie: {len(self.category_index)}")
        print(f"   - Predicati: {len(self.predicate_index)}")
    
    def _get_default_registry_path(self) -> str:
        """Ottiene il percorso di default del registry."""
        current_dir = os.path.dirname(__file__)
        return os.path.join(current_dir, '..', '..', '..', 'neuroglyph_ULTIMATE_registry.json')
    
    def _ensure_registry_loaded(self):
        """Carica il registry solo quando necessario (lazy loading)."""
        if self._registry_loaded:
            return

        try:
            with open(self.registry_path, 'r', encoding='utf-8') as f:
                self.registry = json.load(f)
            print(f"📚 Registry caricato: {len(self.registry.get('approved_symbols', []))} simboli")
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"⚠️ Errore caricamento registry: {e}")
            self.registry = {"approved_symbols": []}

        self._registry_loaded = True

        # Ora crea mappings automatici
        self._create_automatic_mappings()
    
    def _create_base_mappings(self):
        """Crea mappings base per simboli comuni."""
        # Mappings predefiniti per simboli chiave
        base_mappings = {
            # Control Flow
            "⟲": SymbolMapping(
                symbol="⟲",
                category=SymbolCategory.CONTROL_FLOW,
                semantic_predicate="IsLoop",
                logical_rules=[
                    "IsLoop(X) ∧ HasBreak(X) ⇒ CanTerminate(X)",
                    "IsLoop(X) ∧ ¬HasBreak(X) ∧ ¬HasReturn(X) ⇒ InfiniteLoop(X)"
                ],
                inference_patterns=[
                    "loop_termination",
                    "infinite_loop_detection"
                ]
            ),
            
            "⦟": SymbolMapping(
                symbol="⦟",
                category=SymbolCategory.CONTROL_FLOW,
                semantic_predicate="IsConditional",
                logical_rules=[
                    "IsConditional(X) ∧ HasCondition(X, C) ⇒ BranchingLogic(X, C)",
                    "IsConditional(X) ∧ ¬HasElse(X) ⇒ PartialCoverage(X)"
                ],
                inference_patterns=[
                    "conditional_logic",
                    "branch_coverage"
                ]
            ),
            
            "⌮": SymbolMapping(
                symbol="⌮",
                category=SymbolCategory.DATA_STRUCTURE,
                semantic_predicate="IsFunction",
                logical_rules=[
                    "IsFunction(X) ∧ HasParameters(X, P) ⇒ AcceptsInput(X, P)",
                    "IsFunction(X) ∧ HasReturn(X, R) ⇒ ProducesOutput(X, R)",
                    "IsFunction(X) ∧ ¬HasReturn(X) ⇒ ReturnsNone(X)"
                ],
                inference_patterns=[
                    "function_signature",
                    "return_analysis"
                ]
            ),
            
            # Logical Operators
            "⨑": SymbolMapping(
                symbol="⨑",
                category=SymbolCategory.LOGICAL_OPERATOR,
                semantic_predicate="LogicalAnd",
                logical_rules=[
                    "LogicalAnd(A, B) ∧ True(A) ∧ True(B) ⇒ True(LogicalAnd(A, B))",
                    "LogicalAnd(A, B) ∧ (False(A) ∨ False(B)) ⇒ False(LogicalAnd(A, B))"
                ],
                inference_patterns=[
                    "conjunction_evaluation",
                    "short_circuit_and"
                ]
            ),
            
            "⨒": SymbolMapping(
                symbol="⨒",
                category=SymbolCategory.LOGICAL_OPERATOR,
                semantic_predicate="LogicalOr",
                logical_rules=[
                    "LogicalOr(A, B) ∧ (True(A) ∨ True(B)) ⇒ True(LogicalOr(A, B))",
                    "LogicalOr(A, B) ∧ False(A) ∧ False(B) ⇒ False(LogicalOr(A, B))"
                ],
                inference_patterns=[
                    "disjunction_evaluation",
                    "short_circuit_or"
                ]
            ),
            
            # Mathematical
            "∑": SymbolMapping(
                symbol="∑",
                category=SymbolCategory.MATHEMATICAL,
                semantic_predicate="Summation",
                logical_rules=[
                    "Summation(X) ∧ IsIterable(X) ⇒ CanSum(X)",
                    "Summation(X) ∧ Empty(X) ⇒ Result(X, 0)"
                ],
                inference_patterns=[
                    "aggregation_operation",
                    "empty_collection_handling"
                ]
            ),
            
            # Relational
            "≡": SymbolMapping(
                symbol="≡",
                category=SymbolCategory.RELATIONAL,
                semantic_predicate="Equivalent",
                logical_rules=[
                    "Equivalent(A, B) ⇒ Equivalent(B, A)",
                    "Equivalent(A, B) ∧ Equivalent(B, C) ⇒ Equivalent(A, C)"
                ],
                inference_patterns=[
                    "equivalence_symmetry",
                    "equivalence_transitivity"
                ]
            ),
            
            # Temporal
            "⧖": SymbolMapping(
                symbol="⧖",
                category=SymbolCategory.TEMPORAL,
                semantic_predicate="Temporal",
                logical_rules=[
                    "Temporal(X) ∧ Before(X, Y) ⇒ ¬After(X, Y)",
                    "Temporal(X) ∧ During(X, Y) ⇒ Contains(Y, X)"
                ],
                inference_patterns=[
                    "temporal_ordering",
                    "temporal_containment"
                ]
            )
        }
        
        # Aggiungi mappings base
        for symbol, mapping in base_mappings.items():
            self.add_mapping(mapping)

        # I mappings automatici saranno creati al primo accesso al registry
    
    def _create_automatic_mappings(self):
        """Crea mappings automatici per simboli del registry."""
        # Assicura che il registry sia caricato
        if not self.registry or not self.registry.get('approved_symbols'):
            return
        
        for symbol_data in self.registry['approved_symbols']:
            symbol = symbol_data.get('symbol', '')
            if symbol and symbol not in self.symbol_mappings:
                # Inferisci categoria dal contesto
                category = self._infer_category(symbol_data)
                
                # Crea mapping automatico
                mapping = SymbolMapping(
                    symbol=symbol,
                    category=category,
                    semantic_predicate=f"Symbol_{ord(symbol)}",
                    logical_rules=[f"Symbol_{ord(symbol)}(X) ⇒ HasSymbol(X, '{symbol}')"],
                    inference_patterns=["symbol_presence"],
                    confidence=0.5  # Bassa confidenza per mappings automatici
                )
                
                self.add_mapping(mapping)
    
    def _infer_category(self, symbol_data: Dict[str, Any]) -> SymbolCategory:
        """Inferisce la categoria di un simbolo dal contesto."""
        description = symbol_data.get('description', '').lower()
        context = symbol_data.get('context', '').lower()
        
        # Regole di inferenza categoria
        if any(word in description for word in ['loop', 'while', 'for', 'if', 'else']):
            return SymbolCategory.CONTROL_FLOW
        elif any(word in description for word in ['function', 'method', 'class', 'object']):
            return SymbolCategory.DATA_STRUCTURE
        elif any(word in description for word in ['and', 'or', 'not', 'logic']):
            return SymbolCategory.LOGICAL_OPERATOR
        elif any(word in description for word in ['sum', 'math', 'calc', 'number']):
            return SymbolCategory.MATHEMATICAL
        elif any(word in description for word in ['equal', 'compare', 'relation']):
            return SymbolCategory.RELATIONAL
        elif any(word in description for word in ['time', 'temporal', 'sequence']):
            return SymbolCategory.TEMPORAL
        else:
            return SymbolCategory.SEMANTIC
    
    def add_mapping(self, mapping: SymbolMapping):
        """Aggiunge un mapping simbolo."""
        self.symbol_mappings[mapping.symbol] = mapping
        
        # Aggiorna indici
        if mapping.category not in self.category_index:
            self.category_index[mapping.category] = set()
        self.category_index[mapping.category].add(mapping.symbol)
        
        if mapping.semantic_predicate not in self.predicate_index:
            self.predicate_index[mapping.semantic_predicate] = set()
        self.predicate_index[mapping.semantic_predicate].add(mapping.symbol)
    
    def get_mapping(self, symbol: str) -> Optional[SymbolMapping]:
        """Ottiene mapping per un simbolo."""
        return self.symbol_mappings.get(symbol)
    
    def get_predicate(self, symbol: str) -> Optional[str]:
        """Ottiene predicato semantico per un simbolo."""
        mapping = self.get_mapping(symbol)
        return mapping.semantic_predicate if mapping else None
    
    def get_logical_rules(self, symbol: str) -> List[str]:
        """Ottiene regole logiche per un simbolo."""
        mapping = self.get_mapping(symbol)
        return mapping.logical_rules if mapping else []
    
    def get_inference_patterns(self, symbol: str) -> List[str]:
        """Ottiene pattern di inferenza per un simbolo."""
        mapping = self.get_mapping(symbol)
        return mapping.inference_patterns if mapping else []
    
    def symbols_by_category(self, category: SymbolCategory) -> Set[str]:
        """Ottiene simboli per categoria."""
        return self.category_index.get(category, set())
    
    def symbols_by_predicate(self, predicate: str) -> Set[str]:
        """Ottiene simboli per predicato."""
        return self.predicate_index.get(predicate, set())
    
    def create_logical_facts(self, symbols: List[str], context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Crea fatti logici da simboli nel contesto.
        
        Args:
            symbols: Lista di simboli da processare
            context: Contesto di esecuzione
            
        Returns:
            Lista di fatti logici generati
        """
        facts = []
        
        for symbol in symbols:
            mapping = self.get_mapping(symbol)
            if not mapping:
                continue
            
            # Aggiorna contatore utilizzo
            mapping.usage_count += 1
            
            # Crea fatto base
            base_fact = {
                "predicate": mapping.semantic_predicate,
                "symbol": symbol,
                "category": mapping.category.value,
                "confidence": mapping.confidence,
                "context": context
            }
            facts.append(base_fact)
            
            # Crea fatti derivati dalle regole
            for rule in mapping.logical_rules:
                derived_fact = {
                    "predicate": f"Rule_{mapping.semantic_predicate}",
                    "rule": rule,
                    "symbol": symbol,
                    "confidence": mapping.confidence * 0.8  # Ridotta per fatti derivati
                }
                facts.append(derived_fact)
        
        return facts
    
    def get_symbol_statistics(self) -> Dict[str, Any]:
        """Ottiene statistiche sui simboli mappati."""
        total_symbols = len(self.symbol_mappings)
        category_counts = {cat.value: len(symbols) for cat, symbols in self.category_index.items()}
        
        # Simboli più utilizzati
        most_used = sorted(
            self.symbol_mappings.values(),
            key=lambda m: m.usage_count,
            reverse=True
        )[:10]
        
        # Gestisci registry non caricato
        registry_symbols = 0
        mapped_percentage = 0.0

        if self.registry:
            registry_symbols = len(self.registry.get('approved_symbols', []))
            if registry_symbols > 0:
                mapped_percentage = (total_symbols / registry_symbols) * 100

        return {
            "total_symbols": total_symbols,
            "category_distribution": category_counts,
            "most_used_symbols": [
                {"symbol": m.symbol, "usage_count": m.usage_count, "predicate": m.semantic_predicate}
                for m in most_used
            ],
            "registry_symbols": registry_symbols,
            "mapped_percentage": mapped_percentage
        }
