"""
NEUROGLYPH Validation Structures
Strutture dati per validazione e quality control del reasoning
"""

import time
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timezone


class ValidationLevel(Enum):
    """Livelli di validazione."""
    SYNTAX = "syntax"           # Validazione sintattica
    SEMANTIC = "semantic"       # Validazione semantica
    LOGIC = "logic"            # Validazione logica
    QUALITY = "quality"        # Metriche di qualità


class ValidationSeverity(Enum):
    """Severità degli errori di validazione."""
    CRITICAL = "critical"       # Errore critico (blocca esecuzione)
    ERROR = "error"            # Errore grave
    WARNING = "warning"        # Avviso
    INFO = "info"              # Informazione


class ValidationErrorType(Enum):
    """Tipi di errori di validazione."""
    # Syntax errors
    AST_PARSE_ERROR = "ast_parse_error"
    MISSING_LOCATIONS = "missing_locations"
    INDENTATION_ERROR = "indentation_error"
    ORPHAN_NODES = "orphan_nodes"
    
    # Semantic errors
    MISSING_PREMISES = "missing_premises"
    INVALID_REFERENCES = "invalid_references"
    TYPE_MISMATCH = "type_mismatch"
    UNJUSTIFIED_FACTS = "unjustified_facts"
    
    # Logic errors
    CIRCULAR_INFERENCE = "circular_inference"
    UNHANDLED_CONTRADICTION = "unhandled_contradiction"
    INVALID_OPERATOR_APPLICATION = "invalid_operator_application"
    CONFIDENCE_UNDERFLOW = "confidence_underflow"
    
    # Quality issues
    LOW_COVERAGE = "low_coverage"
    EXCESSIVE_DEPTH = "excessive_depth"
    POOR_COHERENCE = "poor_coherence"


@dataclass
class ValidationError:
    """Errore di validazione."""
    
    # Identificazione
    error_type: ValidationErrorType
    severity: ValidationSeverity
    level: ValidationLevel
    
    # Descrizione
    message: str
    description: str = ""
    
    # Localizzazione
    node_id: Optional[str] = None
    step_id: Optional[str] = None
    path_id: Optional[str] = None
    line_number: Optional[int] = None
    
    # Contesto
    context: Dict[str, Any] = field(default_factory=dict)
    suggestions: List[str] = field(default_factory=list)
    
    # Temporali
    detected_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def __post_init__(self):
        """Post-inizializzazione."""
        if not self.description:
            self.description = self.message


@dataclass
class ValidationMetrics:
    """Metriche di qualità della validazione."""
    
    # Coverage metrics
    coverage_percentage: float = 0.0          # % fatti originali riutilizzati
    premise_utilization: float = 0.0          # % premesse utilizzate
    derived_facts_ratio: float = 0.0          # Rapporto fatti derivati/originali
    
    # Depth consistency
    max_depth_reached: int = 0
    avg_path_depth: float = 0.0
    depth_consistency_score: float = 1.0      # Coerenza profondità
    
    # Confidence coherence
    min_confidence: float = 1.0
    avg_confidence: float = 1.0
    confidence_decay_rate: float = 0.0        # Tasso decadimento confidence
    confidence_coherence_score: float = 1.0   # Coerenza confidence
    
    # Logic metrics
    valid_inferences: int = 0
    invalid_inferences: int = 0
    contradictions_detected: int = 0
    circular_references: int = 0
    
    # Quality scores
    overall_quality_score: float = 0.0        # Score qualità complessivo
    syntax_score: float = 1.0
    semantic_score: float = 1.0
    logic_score: float = 1.0
    
    def calculate_overall_score(self):
        """Calcola score qualità complessivo."""
        # Weighted average of all scores
        weights = {
            'syntax': 0.2,
            'semantic': 0.3,
            'logic': 0.3,
            'coverage': 0.1,
            'confidence': 0.1
        }
        
        self.overall_quality_score = (
            self.syntax_score * weights['syntax'] +
            self.semantic_score * weights['semantic'] +
            self.logic_score * weights['logic'] +
            (self.coverage_percentage / 100.0) * weights['coverage'] +
            self.confidence_coherence_score * weights['confidence']
        )


@dataclass
class ValidationResult:
    """Risultato completo della validazione."""
    
    # Stato validazione
    is_valid: bool = True
    validation_passed: bool = True
    
    # Errori per livello
    syntax_errors: List[ValidationError] = field(default_factory=list)
    semantic_errors: List[ValidationError] = field(default_factory=list)
    logic_errors: List[ValidationError] = field(default_factory=list)
    quality_warnings: List[ValidationError] = field(default_factory=list)
    
    # Metriche
    metrics: ValidationMetrics = field(default_factory=ValidationMetrics)
    
    # Temporali
    validation_start: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    validation_end: Optional[datetime] = None
    validation_duration: float = 0.0
    
    # Metadati
    validator_version: str = "1.0"
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_error(self, error: ValidationError):
        """Aggiunge errore al risultato."""
        if error.level == ValidationLevel.SYNTAX:
            self.syntax_errors.append(error)
        elif error.level == ValidationLevel.SEMANTIC:
            self.semantic_errors.append(error)
        elif error.level == ValidationLevel.LOGIC:
            self.logic_errors.append(error)
        elif error.level == ValidationLevel.QUALITY:
            self.quality_warnings.append(error)
        
        # Aggiorna stato validazione
        if error.severity in [ValidationSeverity.CRITICAL, ValidationSeverity.ERROR]:
            self.is_valid = False
            self.validation_passed = False
    
    def finalize(self):
        """Finalizza validazione."""
        self.validation_end = datetime.now(timezone.utc)
        self.validation_duration = (
            self.validation_end - self.validation_start
        ).total_seconds()
        
        # Calcola metriche finali
        self.metrics.calculate_overall_score()
        
        # Aggiorna score per errori
        if self.syntax_errors:
            critical_syntax = sum(1 for e in self.syntax_errors 
                                if e.severity == ValidationSeverity.CRITICAL)
            self.metrics.syntax_score = max(0.0, 1.0 - (critical_syntax * 0.5))
        
        if self.semantic_errors:
            critical_semantic = sum(1 for e in self.semantic_errors 
                                  if e.severity == ValidationSeverity.CRITICAL)
            self.metrics.semantic_score = max(0.0, 1.0 - (critical_semantic * 0.3))
        
        if self.logic_errors:
            critical_logic = sum(1 for e in self.logic_errors 
                               if e.severity == ValidationSeverity.CRITICAL)
            self.metrics.logic_score = max(0.0, 1.0 - (critical_logic * 0.4))
    
    @property
    def total_errors(self) -> int:
        """Numero totale di errori."""
        return (len(self.syntax_errors) + len(self.semantic_errors) + 
                len(self.logic_errors))
    
    @property
    def critical_errors(self) -> int:
        """Numero errori critici."""
        all_errors = (self.syntax_errors + self.semantic_errors +
                     self.logic_errors + self.quality_warnings)
        return sum(1 for e in all_errors if e.severity == ValidationSeverity.CRITICAL)

    @property
    def has_errors(self) -> bool:
        """Verifica se ci sono errori."""
        return self.total_errors > 0

    @property
    def has_critical_errors(self) -> bool:
        """Verifica se ci sono errori critici."""
        return self.critical_errors > 0

    def should_apply_patches(self, quality_threshold: float = 0.7) -> bool:
        """
        Determina se applicare patch basandosi su criticità ed qualità.

        Args:
            quality_threshold: Soglia qualità sotto cui applicare patch

        Returns:
            True se patch dovrebbero essere applicate
        """
        # Applica patch se ci sono errori critici
        if self.has_critical_errors:
            return True

        # Applica patch se qualità sotto soglia
        if self.metrics.overall_quality < quality_threshold:
            return True

        return False

    @property
    def error_summary(self) -> Dict[str, int]:
        """Riassunto errori per tipo."""
        return {
            "syntax": len(self.syntax_errors),
            "semantic": len(self.semantic_errors),
            "logic": len(self.logic_errors),
            "quality": len(self.quality_warnings),
            "total": self.total_errors,
            "critical": self.critical_errors
        }
    
    def get_errors_by_severity(self, severity: ValidationSeverity) -> List[ValidationError]:
        """Ottiene errori per severità."""
        all_errors = (self.syntax_errors + self.semantic_errors + 
                     self.logic_errors + self.quality_warnings)
        return [e for e in all_errors if e.severity == severity]
    
    def get_errors_by_type(self, error_type: ValidationErrorType) -> List[ValidationError]:
        """Ottiene errori per tipo."""
        all_errors = (self.syntax_errors + self.semantic_errors + 
                     self.logic_errors + self.quality_warnings)
        return [e for e in all_errors if e.error_type == error_type]


@dataclass
class ValidationConfig:
    """Configurazione per la validazione."""
    
    # Soglie qualità
    min_coverage_percentage: float = 50.0
    min_confidence_threshold: float = 0.1
    max_depth_allowed: int = 10
    min_quality_score: float = 0.7
    
    # Validazione sintattica
    enable_ast_validation: bool = True
    fix_missing_locations: bool = True
    check_indentation: bool = True
    
    # Validazione semantica
    check_premise_existence: bool = True
    validate_fact_justification: bool = True
    check_type_consistency: bool = True
    
    # Validazione logica
    detect_circular_inference: bool = True
    validate_operator_application: bool = True
    check_contradiction_handling: bool = True
    
    # Performance
    max_validation_time: float = 10.0  # secondi
    enable_detailed_metrics: bool = True
    
    # Output
    include_suggestions: bool = True
    verbose_errors: bool = False
