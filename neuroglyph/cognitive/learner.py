"""
NEUROGLYPH Learner Module
Sistema di meta-apprendimento e costruzione del grafo di conoscenza
"""

import time
import json
import uuid
import hashlib
from typing import Dict, List, Any, Optional, Set, Tuple
from collections import defaultdict

from .learner_structures import (
    LearnerConfig, LearnerState, LearningSession, MetaPattern, 
    KnowledgeNode, KnowledgeEdge, LearningInsight,
    LearningMode, PatternType, ConfidenceLevel
)
from .patcher_structures import PatchResult, LearningPattern
from .validation_structures import ValidationResult
from .sandbox_structures import ExecutionResult


class KnowledgeGraphBuilder:
    """Costruttore del grafo di conoscenza."""
    
    def __init__(self, config: LearnerConfig):
        self.config = config
        self.nodes: Dict[str, KnowledgeNode] = {}
        self.edges: Dict[str, KnowledgeEdge] = {}
        self.node_index: Dict[str, Set[str]] = defaultdict(set)  # type -> node_ids
        
    def add_node(self, node_type: str, content: Dict[str, Any], 
                 embeddings: Optional[List[float]] = None) -> str:
        """Aggiunge nodo al grafo."""
        node_id = self._generate_node_id(node_type, content)
        
        if node_id in self.nodes:
            # Aggiorna nodo esistente
            self.nodes[node_id].update_access()
            return node_id
        
        # Crea nuovo nodo
        node = KnowledgeNode(
            node_id=node_id,
            node_type=node_type,
            content=content,
            embeddings=embeddings
        )
        
        self.nodes[node_id] = node
        self.node_index[node_type].add(node_id)
        
        return node_id
    
    def add_edge(self, source_id: str, target_id: str, 
                 relationship_type: str, weight: float = 1.0) -> str:
        """Aggiunge arco al grafo."""
        edge_id = f"{source_id}_{relationship_type}_{target_id}"
        
        if edge_id in self.edges:
            # Rinforza connessione esistente
            self.edges[edge_id].strengthen()
            return edge_id
        
        # Crea nuovo arco
        edge = KnowledgeEdge(
            edge_id=edge_id,
            source_node_id=source_id,
            target_node_id=target_id,
            relationship_type=relationship_type,
            weight=weight
        )
        
        self.edges[edge_id] = edge
        return edge_id
    
    def find_similar_nodes(self, node_id: str, threshold: float = 0.8) -> List[str]:
        """Trova nodi simili basato su embeddings."""
        if node_id not in self.nodes:
            return []
        
        target_node = self.nodes[node_id]
        if not target_node.embeddings:
            return []
        
        similar_nodes = []
        for other_id, other_node in self.nodes.items():
            if other_id == node_id or not other_node.embeddings:
                continue
            
            similarity = self._calculate_similarity(
                target_node.embeddings, other_node.embeddings
            )
            
            if similarity >= threshold:
                similar_nodes.append(other_id)
        
        return similar_nodes
    
    def _generate_node_id(self, node_type: str, content: Dict[str, Any]) -> str:
        """Genera ID univoco per il nodo."""
        content_str = json.dumps(content, sort_keys=True)
        hash_obj = hashlib.md5(f"{node_type}_{content_str}".encode())
        return f"{node_type}_{hash_obj.hexdigest()[:8]}"
    
    def _calculate_similarity(self, emb1: List[float], emb2: List[float]) -> float:
        """Calcola similarità coseno tra embeddings."""
        if len(emb1) != len(emb2):
            return 0.0
        
        dot_product = sum(a * b for a, b in zip(emb1, emb2))
        norm1 = sum(a * a for a in emb1) ** 0.5
        norm2 = sum(b * b for b in emb2) ** 0.5
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)


class PatternExtractor:
    """Estrattore di pattern da risultati di correzione."""
    
    def __init__(self, config: LearnerConfig):
        self.config = config
        
    def extract_from_patch_results(self, patch_results: List[PatchResult]) -> List[MetaPattern]:
        """Estrae pattern da risultati di patch."""
        patterns = []
        
        # Raggruppa risultati per tipo di errore e strategia
        grouped_results = defaultdict(list)
        
        for result in patch_results:
            if result.success:
                key = f"{result.error_type}_{result.patch_strategy}"
                grouped_results[key].append(result)
        
        # Crea pattern per gruppi con successi multipli
        for key, results in grouped_results.items():
            if len(results) >= self.config.min_pattern_applications:
                pattern = self._create_pattern_from_results(key, results)
                if pattern:
                    patterns.append(pattern)
        
        return patterns
    
    def extract_from_learning_patterns(self, learning_patterns: List[LearningPattern]) -> List[MetaPattern]:
        """Estrae meta-pattern da pattern di apprendimento."""
        meta_patterns = []
        
        for lp in learning_patterns:
            if (lp.effectiveness_score >= self.config.min_pattern_confidence and
                lp.occurrence_count >= self.config.min_pattern_applications):
                
                pattern = MetaPattern(
                    pattern_id=f"meta_{lp.pattern_name}_{uuid.uuid4().hex[:8]}",
                    pattern_name=f"Meta_{lp.pattern_name}",
                    pattern_type=PatternType.ERROR_CORRECTION,
                    trigger_conditions=[lp.error_pattern],
                    action_sequence=[lp.solution_pattern],
                    expected_outcomes=["error_resolution"],
                    success_rate=lp.effectiveness_score,
                    total_applications=lp.occurrence_count,
                    successful_applications=lp.success_count
                )
                
                pattern.update_success_rate()
                meta_patterns.append(pattern)
        
        return meta_patterns
    
    def _create_pattern_from_results(self, key: str, results: List[PatchResult]) -> Optional[MetaPattern]:
        """Crea pattern da risultati raggruppati."""
        if not results:
            return None
        
        # Calcola metriche aggregate
        total_applications = len(results)
        successful_applications = sum(1 for r in results if r.success)
        success_rate = successful_applications / total_applications
        
        # Estrai caratteristiche comuni
        error_types = set(r.error_type for r in results if r.error_type)
        patch_strategies = set(r.patch_strategy for r in results if r.patch_strategy)
        
        if not error_types or not patch_strategies:
            return None
        
        pattern = MetaPattern(
            pattern_id=f"extracted_{key}_{uuid.uuid4().hex[:8]}",
            pattern_name=f"Pattern_{key}",
            pattern_type=PatternType.ERROR_CORRECTION,
            trigger_conditions=list(error_types),
            action_sequence=list(patch_strategies),
            expected_outcomes=["error_resolution", "code_improvement"],
            success_rate=success_rate,
            total_applications=total_applications,
            successful_applications=successful_applications
        )
        
        # Calcola miglioramento medio
        improvements = [r.performance_improvement for r in results 
                       if r.performance_improvement is not None]
        if improvements:
            pattern.average_improvement = sum(improvements) / len(improvements)
        
        pattern.update_success_rate()
        return pattern


class LearnerEngine:
    """Motore principale di apprendimento."""
    
    def __init__(self, config: Optional[LearnerConfig] = None):
        self.config = config or LearnerConfig()
        self.state = LearnerState()
        
        # Componenti
        self.knowledge_graph = KnowledgeGraphBuilder(self.config)
        self.pattern_extractor = PatternExtractor(self.config)
        
        # Storage
        self.meta_patterns: Dict[str, MetaPattern] = {}
        self.insights: Dict[str, LearningInsight] = {}
        
        # Carica stato precedente
        self._load_state()
        
        print("🧠 NGLearner Engine inizializzato")
        print(f"   - Learning mode: {self.config.learning_mode.value}")
        print(f"   - Patterns caricati: {len(self.meta_patterns)}")
        print(f"   - Knowledge nodes: {len(self.knowledge_graph.nodes)}")
    
    def start_learning_session(self, focus_areas: Optional[List[str]] = None) -> str:
        """Inizia una nuova sessione di apprendimento."""
        session = LearningSession(
            session_id=f"session_{uuid.uuid4().hex[:8]}",
            learning_mode=self.config.learning_mode,
            focus_areas=focus_areas or []
        )
        
        self.state.current_session = session
        self.state.total_sessions += 1
        
        print(f"🎓 Sessione di apprendimento iniziata: {session.session_id}")
        return session.session_id
    
    def learn_from_patch_results(self, patch_results: List[PatchResult]) -> List[str]:
        """Apprende da risultati di patch."""
        if not self.state.current_session:
            self.start_learning_session()
        
        # Estrai pattern
        new_patterns = self.pattern_extractor.extract_from_patch_results(patch_results)
        
        pattern_ids = []
        for pattern in new_patterns:
            pattern_id = self._integrate_pattern(pattern)
            if pattern_id:
                pattern_ids.append(pattern_id)
                self.state.current_session.patterns_discovered.append(pattern_id)
        
        # Aggiorna statistiche sessione
        self.state.current_session.total_errors_processed += len(patch_results)
        self.state.current_session.successful_corrections += sum(
            1 for r in patch_results if r.success
        )
        
        return pattern_ids
    
    def _integrate_pattern(self, pattern: MetaPattern) -> Optional[str]:
        """Integra nuovo pattern nel sistema."""
        # Cerca pattern simili esistenti
        similar_patterns = self._find_similar_patterns(pattern)
        
        if similar_patterns:
            # Merge con pattern esistente più simile
            best_match = similar_patterns[0]
            self._merge_patterns(best_match, pattern)
            return best_match
        else:
            # Aggiungi come nuovo pattern
            self.meta_patterns[pattern.pattern_id] = pattern
            
            # Aggiungi al grafo di conoscenza
            self._add_pattern_to_knowledge_graph(pattern)
            
            return pattern.pattern_id
    
    def _find_similar_patterns(self, pattern: MetaPattern) -> List[str]:
        """Trova pattern simili esistenti."""
        similar = []
        
        for existing_id, existing_pattern in self.meta_patterns.items():
            if existing_pattern.pattern_type != pattern.pattern_type:
                continue
            
            # Calcola similarità basata su trigger e azioni
            trigger_similarity = self._calculate_set_similarity(
                set(pattern.trigger_conditions),
                set(existing_pattern.trigger_conditions)
            )
            
            action_similarity = self._calculate_set_similarity(
                set(pattern.action_sequence),
                set(existing_pattern.action_sequence)
            )
            
            overall_similarity = (trigger_similarity + action_similarity) / 2
            
            if overall_similarity >= self.config.similarity_threshold:
                similar.append(existing_id)
        
        return similar
    
    def _calculate_set_similarity(self, set1: Set[str], set2: Set[str]) -> float:
        """Calcola similarità tra due set."""
        if not set1 and not set2:
            return 1.0
        if not set1 or not set2:
            return 0.0
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def _merge_patterns(self, existing_id: str, new_pattern: MetaPattern):
        """Merge nuovo pattern con uno esistente."""
        existing = self.meta_patterns[existing_id]
        
        # Aggiorna statistiche
        total_apps = existing.total_applications + new_pattern.total_applications
        successful_apps = existing.successful_applications + new_pattern.successful_applications
        
        existing.total_applications = total_apps
        existing.successful_applications = successful_apps
        existing.update_success_rate()
        
        # Merge trigger conditions e azioni
        existing.trigger_conditions = list(set(existing.trigger_conditions + new_pattern.trigger_conditions))
        existing.action_sequence = list(set(existing.action_sequence + new_pattern.action_sequence))
        
        existing.last_updated = time.time()
        
        print(f"🔄 Pattern merged: {existing_id} (apps: {total_apps}, success: {existing.success_rate:.3f})")
    
    def _add_pattern_to_knowledge_graph(self, pattern: MetaPattern):
        """Aggiunge pattern al grafo di conoscenza."""
        # Crea nodo per il pattern
        pattern_node_id = self.knowledge_graph.add_node(
            "meta_pattern",
            {
                "pattern_id": pattern.pattern_id,
                "pattern_name": pattern.pattern_name,
                "pattern_type": pattern.pattern_type.value,
                "success_rate": pattern.success_rate
            }
        )
        
        # Crea nodi per trigger conditions
        for trigger in pattern.trigger_conditions:
            trigger_node_id = self.knowledge_graph.add_node(
                "trigger_condition",
                {"condition": trigger}
            )
            
            # Collega trigger al pattern
            self.knowledge_graph.add_edge(
                trigger_node_id, pattern_node_id, "triggers", pattern.success_rate
            )
        
        # Crea nodi per azioni
        for action in pattern.action_sequence:
            action_node_id = self.knowledge_graph.add_node(
                "action",
                {"action": action}
            )
            
            # Collega pattern all'azione
            self.knowledge_graph.add_edge(
                pattern_node_id, action_node_id, "executes", pattern.success_rate
            )
    
    def _load_state(self):
        """Carica stato precedente."""
        if self.config.save_patterns:
            try:
                with open(self.config.patterns_file, 'r') as f:
                    data = json.load(f)
                    # Caricamento semplificato per ora
                    print(f"📁 Caricati {len(data.get('patterns', []))} pattern dal file")
            except (FileNotFoundError, json.JSONDecodeError):
                print("📁 Nessun file di pattern precedente trovato")
    
    def _save_state(self):
        """Salva stato corrente."""
        if self.config.save_patterns:
            try:
                data = {
                    "patterns": [
                        {
                            "pattern_id": p.pattern_id,
                            "pattern_name": p.pattern_name,
                            "success_rate": p.success_rate,
                            "total_applications": p.total_applications
                        }
                        for p in self.meta_patterns.values()
                    ],
                    "timestamp": time.time()
                }
                
                with open(self.config.patterns_file, 'w') as f:
                    json.dump(data, f, indent=2)
                    
                print(f"💾 Salvati {len(self.meta_patterns)} pattern")
            except Exception as e:
                print(f"⚠️ Errore nel salvataggio pattern: {e}")


class NGLearner:
    """Modulo principale di apprendimento NEUROGLYPH."""
    
    def __init__(self, config: Optional[LearnerConfig] = None):
        self.config = config or LearnerConfig()
        self.engine = LearnerEngine(self.config)
        
        print("🧠 NGLearner inizializzato")
    
    def learn(self, patch_results: List[PatchResult], 
              learning_patterns: Optional[List[LearningPattern]] = None) -> Dict[str, Any]:
        """
        Metodo principale di apprendimento.
        
        Args:
            patch_results: Risultati di patch da cui apprendere
            learning_patterns: Pattern di apprendimento esistenti
            
        Returns:
            Risultati dell'apprendimento
        """
        session_id = self.engine.start_learning_session()
        
        # Apprendi da patch results
        new_pattern_ids = self.engine.learn_from_patch_results(patch_results)
        
        # Apprendi da learning patterns se forniti
        if learning_patterns:
            meta_patterns = self.engine.pattern_extractor.extract_from_learning_patterns(learning_patterns)
            for pattern in meta_patterns:
                pattern_id = self.engine._integrate_pattern(pattern)
                if pattern_id:
                    new_pattern_ids.append(pattern_id)
        
        # Finalizza sessione
        if self.engine.state.current_session:
            self.engine.state.current_session.finalize()
        
        # Aggiorna statistiche
        self.engine.state.update_statistics(
            list(self.engine.meta_patterns.values()),
            len(self.engine.knowledge_graph.nodes),
            len(self.engine.knowledge_graph.edges)
        )
        
        # Salva stato
        self.engine._save_state()
        
        return {
            "session_id": session_id,
            "new_patterns_discovered": len(new_pattern_ids),
            "total_patterns": len(self.engine.meta_patterns),
            "learning_rate": self.engine.state.overall_learning_rate,
            "pattern_quality": self.engine.state.average_pattern_quality,
            "knowledge_graph_size": len(self.engine.knowledge_graph.nodes)
        }
    
    def get_applicable_patterns(self, context: Dict[str, Any]) -> List[MetaPattern]:
        """Ottiene pattern applicabili al contesto dato."""
        applicable = []
        
        for pattern in self.engine.meta_patterns.values():
            if self._is_pattern_applicable(pattern, context):
                applicable.append(pattern)
        
        # Ordina per confidenza e successo
        applicable.sort(
            key=lambda p: (p.confidence_level.value, p.success_rate),
            reverse=True
        )
        
        return applicable
    
    def _is_pattern_applicable(self, pattern: MetaPattern, context: Dict[str, Any]) -> bool:
        """Verifica se un pattern è applicabile al contesto."""
        # Logica semplificata - in implementazione reale userebbe embeddings e matching semantico
        error_type = context.get("error_type", "")
        
        return any(trigger in error_type for trigger in pattern.trigger_conditions)
    
    def get_learning_statistics(self) -> Dict[str, Any]:
        """Ottiene statistiche di apprendimento."""
        return {
            "total_sessions": self.engine.state.total_sessions,
            "total_patterns": len(self.engine.meta_patterns),
            "knowledge_nodes": len(self.engine.knowledge_graph.nodes),
            "knowledge_edges": len(self.engine.knowledge_graph.edges),
            "average_pattern_quality": self.engine.state.average_pattern_quality,
            "knowledge_graph_density": self.engine.state.knowledge_graph_density,
            "learning_mode": self.config.learning_mode.value
        }
