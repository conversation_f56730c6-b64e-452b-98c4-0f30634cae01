"""
NEUROGLYPH Sandbox
Ambiente di esecuzione sicura per codice Python
"""

import sys
import time
import signal
import threading
from io import String<PERSON>
from contextlib import redirect_stdout, redirect_stderr
from typing import Optional, Dict, Any

from .sandbox_structures import (
    ExecutionContext, ExecutionResult, ExecutionStatus, ExecutionMode,
    SecurityLevel, ResourceLimits, SandboxConfig
)
from .security_monitor import SecurityMonitor


class TimeoutException(Exception):
    """Eccezione per timeout di esecuzione."""
    pass


class NGSandbox:
    """
    Sandbox sicuro per l'esecuzione di codice Python.
    Fornisce isolamento, monitoring delle risorse e controlli di sicurezza.
    """
    
    def __init__(self, config: Optional[SandboxConfig] = None):
        """
        Inizializza il sandbox.
        
        Args:
            config: Configurazione sandbox
        """
        self.config = config or SandboxConfig()
        self.security_monitor = SecurityMonitor(self.config)
        self.active_executions = {}
        
        print("🏖️ NGSandbox inizializzato")
        print(f"   - Default timeout: {self.config.default_timeout}s")
        print(f"   - Default memory limit: {self.config.default_memory_limit}MB")
        print(f"   - Security level: {self.config.default_security_level}")
        print(f"   - Execution logging: {self.config.enable_execution_logging}")
    
    def execute(self, context: ExecutionContext) -> ExecutionResult:
        """
        Esegue codice nel sandbox.
        
        Args:
            context: Contesto di esecuzione
            
        Returns:
            Risultato dell'esecuzione
        """
        result = ExecutionResult(execution_context=context)
        
        try:
            # Pre-execution security check
            violations = self.security_monitor.analyze_code_security(
                context.code, context.security_level
            )
            
            if violations:
                critical_violations = [v for v in violations if v.severity == "critical"]
                if critical_violations:
                    result.status = ExecutionStatus.BLOCKED
                    result.error_type = "security_violation"
                    result.error_message = f"Critical security violations: {len(critical_violations)}"
                    result.metadata["violations"] = [
                        {"type": v.violation_type, "message": v.message} 
                        for v in critical_violations
                    ]
                    result.finalize()
                    return result
            
            # Setup execution environment
            safe_globals = self._create_safe_environment(context)
            safe_locals = context.locals_dict.copy()
            
            # Execute with monitoring
            with self.security_monitor.monitor_execution(context.resource_limits):
                result = self._execute_with_timeout(context, safe_globals, safe_locals, result)
            
            # Post-execution cleanup
            result.resource_usage = self.security_monitor.resource_usage
            
        except Exception as e:
            result.status = ExecutionStatus.FAILED
            result.error_type = type(e).__name__
            result.error_message = str(e)
            import traceback
            result.traceback = traceback.format_exc()
        
        result.finalize()
        
        if self.config.enable_execution_logging:
            self._log_execution(result)
        
        return result
    
    def _create_safe_environment(self, context: ExecutionContext) -> Dict[str, Any]:
        """Crea ambiente di esecuzione sicuro."""
        # Inizia con globals sicuri dal security monitor
        safe_globals = self.security_monitor.create_safe_globals(context.security_level)
        
        # Aggiungi globals personalizzati dal context
        for key, value in context.globals_dict.items():
            if self._is_safe_value(key, value, context.security_level):
                safe_globals[key] = value
        
        # Aggiungi funzioni di utilità sicure
        safe_globals.update({
            '__name__': '__sandbox__',
            '__doc__': None,
            '__package__': None,
        })
        
        return safe_globals
    
    def _is_safe_value(self, key: str, value: Any, security_level: SecurityLevel) -> bool:
        """Verifica se un valore è sicuro da includere nell'ambiente."""
        # Blocca nomi pericolosi
        dangerous_names = ['__builtins__', '__import__', 'exec', 'eval', 'open']
        if key in dangerous_names:
            return False
        
        # Blocca moduli pericolosi
        if hasattr(value, '__name__') and hasattr(value, '__file__'):
            module_name = getattr(value, '__name__', '')
            if module_name in self.config.blocked_modules:
                return False
        
        # Blocca funzioni pericolose
        if callable(value):
            func_name = getattr(value, '__name__', '')
            if func_name in self.config.blocked_builtins:
                return False
        
        return True
    
    def _execute_with_timeout(self, context: ExecutionContext, safe_globals: Dict[str, Any], 
                             safe_locals: Dict[str, Any], result: ExecutionResult) -> ExecutionResult:
        """Esegue codice con timeout."""
        
        # Setup timeout
        timeout = min(context.resource_limits.max_execution_time, self.config.max_timeout)
        
        # Capture output
        stdout_capture = StringIO()
        stderr_capture = StringIO()
        
        def timeout_handler(signum, frame):
            raise TimeoutException(f"Execution timeout after {timeout}s")
        
        # Setup signal handler (solo su Unix)
        old_handler = None
        if hasattr(signal, 'SIGALRM'):
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(int(timeout))
        
        try:
            result.status = ExecutionStatus.RUNNING
            
            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                if context.mode == ExecutionMode.EVAL:
                    # Valuta espressione
                    result.result = eval(context.code, safe_globals, safe_locals)
                elif context.mode == ExecutionMode.EXEC:
                    # Esegui statement
                    exec(context.code, safe_globals, safe_locals)
                    result.result = safe_locals.get('result', None)
                elif context.mode == ExecutionMode.COMPILE:
                    # Solo compila
                    result.result = compile(context.code, '<sandbox>', 'exec')
                elif context.mode == ExecutionMode.VALIDATE:
                    # Solo valida sintassi
                    compile(context.code, '<sandbox>', 'exec')
                    result.result = True
            
            result.status = ExecutionStatus.COMPLETED
            
        except TimeoutException:
            result.status = ExecutionStatus.TIMEOUT
            result.error_type = "TimeoutException"
            result.error_message = f"Execution timed out after {timeout}s"
            
        except SyntaxError as e:
            result.status = ExecutionStatus.FAILED
            result.error_type = "SyntaxError"
            result.error_message = str(e)
            result.metadata["line_number"] = e.lineno
            result.metadata["text"] = e.text
            
        except Exception as e:
            result.status = ExecutionStatus.FAILED
            result.error_type = type(e).__name__
            result.error_message = str(e)
            import traceback
            result.traceback = traceback.format_exc()
            
        finally:
            # Cleanup timeout
            if hasattr(signal, 'SIGALRM'):
                signal.alarm(0)
                if old_handler:
                    signal.signal(signal.SIGALRM, old_handler)
            
            # Capture output
            result.stdout = stdout_capture.getvalue()
            result.stderr = stderr_capture.getvalue()
            
            # Update resource usage
            result.resource_usage.stdout_size = len(result.stdout)
            result.resource_usage.stderr_size = len(result.stderr)
        
        return result
    
    def execute_code(self, code: str, timeout: Optional[float] = None, 
                    security_level: Optional[SecurityLevel] = None,
                    globals_dict: Optional[Dict[str, Any]] = None) -> ExecutionResult:
        """
        Metodo semplificato per eseguire codice.
        
        Args:
            code: Codice da eseguire
            timeout: Timeout in secondi
            security_level: Livello di sicurezza
            globals_dict: Variabili globali
            
        Returns:
            Risultato dell'esecuzione
        """
        # Crea contesto
        context = ExecutionContext(
            code=code,
            mode=ExecutionMode.EXEC,
            security_level=security_level or self.config.default_security_level,
            globals_dict=globals_dict or {}
        )
        
        # Imposta timeout
        if timeout:
            context.resource_limits.max_execution_time = min(timeout, self.config.max_timeout)
        
        return self.execute(context)
    
    def evaluate_expression(self, expression: str, timeout: Optional[float] = None,
                           globals_dict: Optional[Dict[str, Any]] = None) -> ExecutionResult:
        """
        Valuta un'espressione Python.
        
        Args:
            expression: Espressione da valutare
            timeout: Timeout in secondi
            globals_dict: Variabili globali
            
        Returns:
            Risultato della valutazione
        """
        context = ExecutionContext(
            code=expression,
            mode=ExecutionMode.EVAL,
            security_level=self.config.default_security_level,
            globals_dict=globals_dict or {}
        )
        
        if timeout:
            context.resource_limits.max_execution_time = min(timeout, self.config.max_timeout)
        
        return self.execute(context)
    
    def validate_code(self, code: str) -> ExecutionResult:
        """
        Valida sintassi del codice senza eseguirlo.
        
        Args:
            code: Codice da validare
            
        Returns:
            Risultato della validazione
        """
        context = ExecutionContext(
            code=code,
            mode=ExecutionMode.VALIDATE,
            security_level=SecurityLevel.MINIMAL
        )
        
        return self.execute(context)

    def execute_reasoning_graph(self, reasoning_graph) -> ExecutionResult:
        """
        Esegue un ReasoningGraph nel sandbox.

        Args:
            reasoning_graph: Grafo di reasoning da eseguire

        Returns:
            Risultato dell'esecuzione
        """
        # Usa duck typing per evitare import circolari

        # Crea contesto di esecuzione per il reasoning graph
        from .sandbox_structures import ResourceLimits

        resource_limits = ResourceLimits(
            max_execution_time=self.config.default_timeout,
            max_memory_mb=self.config.default_memory_limit
        )

        context = ExecutionContext(
            code="# Reasoning Graph Execution",
            security_level=self.config.default_security_level,
            resource_limits=resource_limits,
            description="NEUROGLYPH Reasoning Graph Execution"
        )

        result = ExecutionResult(
            execution_context=context,
            metadata={
                "execution_type": "reasoning_graph",
                "graph_id": reasoning_graph.graph_id,
                "total_facts": len(reasoning_graph.all_facts),
                "total_paths": len(reasoning_graph.paths)
            }
        )

        try:
            # Simula esecuzione del reasoning graph
            # In una implementazione completa, questo convertirebbe il grafo in codice eseguibile

            # Estrai informazioni dal grafo
            facts_count = len(reasoning_graph.all_facts)
            paths_count = len(reasoning_graph.paths)
            contradictions_count = len(reasoning_graph.contradictions)

            # Simula validazione e esecuzione
            if facts_count == 0:
                result.success = False
                result.error_message = "No facts to execute"
                result.error_type = "EmptyReasoningGraph"
            elif contradictions_count > 0:
                result.success = False
                result.error_message = f"Contradictions detected: {contradictions_count}"
                result.error_type = "LogicalContradiction"
            else:
                # Esecuzione riuscita
                result.success = True
                result.result = f"Reasoning graph executed successfully: {facts_count} facts, {paths_count} paths"

                # Aggiungi metriche di esecuzione
                result.execution_metrics = {
                    "facts_processed": facts_count,
                    "paths_evaluated": paths_count,
                    "contradictions_found": contradictions_count,
                    "execution_mode": "reasoning_graph"
                }

            # Log dell'esecuzione
            if self.config.enable_execution_logging:
                print(f"🏖️ Reasoning graph executed: {reasoning_graph.graph_id}")
                print(f"   - Facts: {facts_count}")
                print(f"   - Paths: {paths_count}")
                print(f"   - Success: {result.success}")

        except Exception as e:
            result.success = False
            result.error_message = f"Reasoning graph execution failed: {str(e)}"
            result.error_type = type(e).__name__

            if self.config.enable_execution_logging:
                print(f"❌ Reasoning graph execution failed: {str(e)}")

        finally:
            result.finalize()

        return result

    def test_patch_candidate(self, patch_candidate, original_code: str, verbose: bool = False) -> dict:
        """
        Testa un patch candidate nel sandbox (Fase 2.2).

        Args:
            patch_candidate: Candidato patch da testare
            original_code: Codice originale per confronto
            verbose: Flag per logging dettagliato

        Returns:
            Risultati del test con metriche comparative
        """
        if verbose:
            print(f"🧪 Testing patch candidate: {patch_candidate.patch_id}")

        # Applica patch al codice
        try:
            patched_code = self._apply_patch_to_code(patch_candidate, original_code)
            if verbose:
                print(f"   Patch applicata, codice lunghezza: {len(patched_code)}")
        except Exception as e:
            return {
                "success": False,
                "error": f"Patch application failed: {str(e)}",
                "patch_id": patch_candidate.patch_id,
                "compilation_success": False,
                "execution_success": False,
                "validation_improvement": False
            }

        # Test 1: Compilazione
        compilation_result = self._test_compilation(patched_code, verbose)

        # Test 2: Esecuzione (se compila)
        execution_result = None
        if compilation_result["success"]:
            execution_result = self._test_execution(patched_code, verbose)

        # Test 3: Validazione comparativa
        validation_result = self._test_validation_improvement(
            original_code, patched_code, verbose
        )

        # Combina risultati
        test_results = {
            "success": compilation_result["success"] and (execution_result is None or execution_result["success"]),
            "patch_id": patch_candidate.patch_id,
            "compilation_success": compilation_result["success"],
            "compilation_error": compilation_result.get("error"),
            "execution_success": execution_result["success"] if execution_result else None,
            "execution_error": execution_result.get("error") if execution_result else None,
            "validation_improvement": validation_result["improved"],
            "validation_metrics": validation_result.get("metrics"),
            "overall_score": self._calculate_patch_score(compilation_result, execution_result, validation_result)
        }

        if verbose:
            print(f"   Test completato: success={test_results['success']}, score={test_results['overall_score']:.3f}")

        return test_results

    def _apply_patch_to_code(self, patch_candidate, original_code: str) -> str:
        """Applica patch al codice originale."""
        # Implementazione semplificata - in realtà dovrebbe usare patch_candidate.changes
        patched_code = original_code

        # Se il patch ha suggerimenti di modifica, applicali
        if hasattr(patch_candidate, 'suggested_fix') and patch_candidate.suggested_fix:
            # Per ora, aggiungi il fix come commento + codice
            patched_code += f"\n# PATCH: {patch_candidate.suggested_fix}\n"

            # Logica di patch semplificata
            if "missing return" in patch_candidate.suggested_fix.lower():
                # Aggiungi return None se manca
                if "def " in patched_code and "return" not in patched_code:
                    lines = patched_code.split('\n')
                    for i, line in enumerate(lines):
                        if line.strip().startswith('def ') and ':' in line:
                            # Trova la fine della funzione e aggiungi return
                            indent = len(line) - len(line.lstrip())
                            lines.insert(i + 2, ' ' * (indent + 4) + 'return None')
                            break
                    patched_code = '\n'.join(lines)

        return patched_code

    def _test_compilation(self, code: str, verbose: bool = False) -> dict:
        """Testa compilazione del codice."""
        try:
            compile(code, '<patch_test>', 'exec')
            if verbose:
                print(f"   ✅ Compilation: SUCCESS")
            return {"success": True}
        except SyntaxError as e:
            if verbose:
                print(f"   ❌ Compilation: FAILED - {str(e)}")
            return {"success": False, "error": str(e)}
        except Exception as e:
            if verbose:
                print(f"   ❌ Compilation: ERROR - {str(e)}")
            return {"success": False, "error": str(e)}

    def _test_execution(self, code: str, verbose: bool = False) -> dict:
        """Testa esecuzione del codice nel sandbox."""
        try:
            result = self.execute_code(code, timeout=2.0)

            if verbose:
                status = "SUCCESS" if result.success else "FAILED"
                print(f"   🏃 Execution: {status}")
                if not result.success and result.error_message:
                    print(f"      Error: {result.error_message}")

            return {
                "success": result.success,
                "error": result.error_message if not result.success else None,
                "output": result.stdout,
                "execution_time": result.duration
            }
        except Exception as e:
            if verbose:
                print(f"   ❌ Execution: EXCEPTION - {str(e)}")
            return {"success": False, "error": str(e)}

    def _test_validation_improvement(self, original_code: str, patched_code: str, verbose: bool = False) -> dict:
        """Testa se la patch migliora la validazione."""
        # Implementazione semplificata - dovrebbe usare NGSelfCheck
        try:
            # Conta errori sintattici semplici
            original_errors = self._count_simple_errors(original_code)
            patched_errors = self._count_simple_errors(patched_code)

            improved = patched_errors < original_errors

            if verbose:
                print(f"   🔍 Validation: {'IMPROVED' if improved else 'NO_CHANGE'}")
                print(f"      Original errors: {original_errors}, Patched errors: {patched_errors}")

            return {
                "improved": improved,
                "metrics": {
                    "original_errors": original_errors,
                    "patched_errors": patched_errors,
                    "error_reduction": original_errors - patched_errors
                }
            }
        except Exception as e:
            if verbose:
                print(f"   ❌ Validation: ERROR - {str(e)}")
            return {"improved": False, "error": str(e)}

    def _count_simple_errors(self, code: str) -> int:
        """Conta errori sintattici semplici."""
        errors = 0

        # Controlla sintassi base
        try:
            compile(code, '<validation_test>', 'exec')
        except SyntaxError:
            errors += 1

        # Controlla pattern problematici
        lines = code.split('\n')
        for line in lines:
            stripped = line.strip()

            # Funzione senza return
            if stripped.startswith('def ') and ':' in stripped:
                # Cerca return nelle linee successive (semplificato)
                has_return = any('return' in l for l in lines)
                if not has_return:
                    errors += 1

            # Indentazione inconsistente (semplificato)
            if stripped and not stripped.startswith('#'):
                if line.startswith(' ') and len(line) - len(line.lstrip()) % 4 != 0:
                    errors += 1

        return errors

    def _calculate_patch_score(self, compilation_result: dict, execution_result: dict, validation_result: dict) -> float:
        """Calcola score complessivo della patch."""
        score = 0.0

        # Compilazione (40%)
        if compilation_result["success"]:
            score += 0.4

        # Esecuzione (30%)
        if execution_result and execution_result["success"]:
            score += 0.3
        elif execution_result is None:  # Non testata perché non compila
            pass

        # Miglioramento validazione (30%)
        if validation_result["improved"]:
            score += 0.3

        return score

    def _log_execution(self, result: ExecutionResult):
        """Log dell'esecuzione."""
        if not self.config.enable_execution_logging:
            return
        
        status_emoji = {
            ExecutionStatus.COMPLETED: "✅",
            ExecutionStatus.FAILED: "❌", 
            ExecutionStatus.TIMEOUT: "⏰",
            ExecutionStatus.BLOCKED: "🚫"
        }.get(result.status, "❓")
        
        print(f"🏖️ Sandbox execution {status_emoji}")
        print(f"   - Status: {result.status.value}")
        print(f"   - Duration: {result.duration:.3f}s")
        print(f"   - Memory: {result.resource_usage.peak_memory_mb:.1f}MB")
        
        if result.error_message:
            print(f"   - Error: {result.error_type}: {result.error_message}")
        
        if result.stdout:
            lines = result.stdout.split('\n')[:3]  # Prime 3 linee
            print(f"   - Output: {lines[0][:50]}...")
    
    def get_security_violations(self) -> list:
        """Ottiene violazioni di sicurezza rilevate."""
        return self.security_monitor.get_violations()
    
    def clear_security_violations(self):
        """Pulisce violazioni di sicurezza."""
        self.security_monitor.clear_violations()
    
    def is_code_safe(self, code: str, security_level: Optional[SecurityLevel] = None) -> bool:
        """
        Verifica se il codice è sicuro da eseguire.
        
        Args:
            code: Codice da verificare
            security_level: Livello di sicurezza
            
        Returns:
            True se sicuro
        """
        level = security_level or self.config.default_security_level
        violations = self.security_monitor.analyze_code_security(code, level)
        critical_violations = [v for v in violations if v.severity == "critical"]
        return len(critical_violations) == 0
