"""
Test per Patch Testing & Validation - Ciclo error → patch → test → learn
"""

import pytest
import sys
import os

# Aggiungi path per import
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from neuroglyph.cognitive.patch_generator import PatchGenerator
from neuroglyph.cognitive.sandbox import NGSandbox
from neuroglyph.cognitive.validation_structures import ValidationResult, ValidationMetrics
from neuroglyph.cognitive.patcher_structures import (
    ErrorAnalysis, ErrorType, PatchCandidate, PatchType, PatcherConfig
)


class TestPatchTesting:
    """Test per patch testing e validation."""
    
    def test_validation_result_criticality(self):
        """Test logica criticità in ValidationResult."""
        # Crea metriche con errori critici
        metrics = ValidationMetrics(
            overall_quality=0.6,
            syntax_score=0.8,
            semantic_score=0.5,
            logic_score=0.7,
            performance_score=0.9
        )
        
        # Crea validation result con errori critici
        validation_result = ValidationResult(
            syntax_errors=[],
            semantic_errors=[{"severity": "critical", "message": "Critical semantic error"}],
            logic_errors=[],
            quality_warnings=[],
            metrics=metrics
        )
        
        # Test proprietà criticità
        assert validation_result.has_errors
        assert validation_result.has_critical_errors
        assert validation_result.should_apply_patches(quality_threshold=0.7)
        
        print("✅ ValidationResult criticality logic: PASS")
    
    def test_validation_result_quality_threshold(self):
        """Test soglia qualità per patch application."""
        # Metriche con qualità bassa ma nessun errore critico
        metrics = ValidationMetrics(
            overall_quality=0.5,  # Sotto soglia 0.7
            syntax_score=0.8,
            semantic_score=0.6,
            logic_score=0.7,
            performance_score=0.9
        )
        
        validation_result = ValidationResult(
            syntax_errors=[],
            semantic_errors=[{"severity": "warning", "message": "Non-critical warning"}],
            logic_errors=[],
            quality_warnings=[],
            metrics=metrics
        )
        
        # Test soglia qualità
        assert validation_result.has_errors
        assert not validation_result.has_critical_errors
        assert validation_result.should_apply_patches(quality_threshold=0.7)  # Qualità < 0.7
        assert not validation_result.should_apply_patches(quality_threshold=0.4)  # Qualità > 0.4
        
        print("✅ ValidationResult quality threshold: PASS")
    
    def test_sandbox_patch_testing(self):
        """Test testing patch nel sandbox."""
        sandbox = NGSandbox()
        
        # Codice con errore (funzione senza return)
        original_code = """
def calculate_sum(numbers):
    total = 0
    for num in numbers:
        total += num
    # Missing return statement
"""
        
        # Crea patch candidate
        patch_candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            patch_description="Add missing return statement",
            suggested_fix="missing return statement",
            confidence=0.8
        )
        
        # Testa patch
        test_results = sandbox.test_patch_candidate(patch_candidate, original_code, verbose=True)
        
        # Verifica risultati
        assert "success" in test_results
        assert "compilation_success" in test_results
        assert "validation_improvement" in test_results
        assert "overall_score" in test_results
        
        print(f"   Test results: {test_results}")
        print("✅ Sandbox patch testing: PASS")
    
    def test_patch_generator_with_testing(self):
        """Test PatchGenerator con testing integrato."""
        generator = PatchGenerator()
        sandbox = NGSandbox()
        
        # Crea error analysis
        error_analysis = ErrorAnalysis(
            error_id="test_error_001",
            error_type=ErrorType.SYNTAX_ERROR,
            error_message="Missing return statement",
            error_pattern="missing_return",
            severity="medium",
            impact_score=0.6,
            error_context="def function without return"
        )
        
        # Codice con errore
        original_code = """
def process_data(data):
    result = []
    for item in data:
        if item > 0:
            result.append(item * 2)
    # Missing return result
"""
        
        # Genera e testa patch
        tested_patches = generator.generate_and_test_patches(
            error_analysis, original_code, sandbox, verbose=True
        )
        
        # Verifica risultati
        assert len(tested_patches) > 0
        
        for patch in tested_patches:
            assert hasattr(patch, 'test_results')
            assert hasattr(patch, 'success_probability')
            assert patch.status in ["tested_success", "tested_failed"]
            
            print(f"   Patch {patch.patch_id}: {patch.status} (score: {patch.success_probability:.3f})")
        
        # Verifica ordinamento per score
        scores = [p.success_probability for p in tested_patches]
        assert scores == sorted(scores, reverse=True)
        
        print("✅ PatchGenerator with testing: PASS")
    
    def test_patch_success_rate_calculation(self):
        """Test calcolo success rate delle patch."""
        sandbox = NGSandbox()
        
        # Test con codice che compila ma ha errori logici
        original_code = """
def divide_numbers(a, b):
    return a / b  # No zero division check
"""
        
        patch_candidate = PatchCandidate(
            patch_type=PatchType.CODE_FIX,
            patch_description="Add zero division check",
            suggested_fix="zero division protection",
            confidence=0.9
        )
        
        test_results = sandbox.test_patch_candidate(patch_candidate, original_code, verbose=True)
        
        # Verifica score calculation
        assert 0.0 <= test_results["overall_score"] <= 1.0
        
        # Score dovrebbe essere > 0 se compila
        if test_results["compilation_success"]:
            assert test_results["overall_score"] >= 0.4  # Almeno 40% per compilazione
        
        print(f"   Overall score: {test_results['overall_score']:.3f}")
        print("✅ Patch success rate calculation: PASS")
    
    def test_error_to_patch_to_test_cycle(self):
        """Test ciclo completo error → patch → test → learn."""
        print("🔄 Testing complete error → patch → test cycle...")
        
        # Setup
        generator = PatchGenerator()
        sandbox = NGSandbox()
        
        # Simula errore critico
        error_analysis = ErrorAnalysis(
            error_id="critical_error_001",
            error_type=ErrorType.RUNTIME_ERROR,
            error_message="name 'undefined_var' is not defined",
            error_pattern="undefined_variable",
            severity="critical",
            impact_score=0.9,
            error_context="undefined_var.process()"
        )
        
        # Codice con errore critico
        original_code = """
def process_items():
    items = [1, 2, 3, 4, 5]
    result = undefined_var.process(items)  # undefined_var not defined
    return result
"""
        
        # Step 1: Genera patch candidates
        candidates = generator.generate_patches(error_analysis)
        assert len(candidates) > 0
        print(f"   Step 1: Generated {len(candidates)} patch candidates")
        
        # Step 2: Testa patch nel sandbox
        tested_candidates = generator.generate_and_test_patches(
            error_analysis, original_code, sandbox, verbose=True
        )
        assert len(tested_candidates) > 0
        print(f"   Step 2: Tested {len(tested_candidates)} candidates")
        
        # Step 3: Analizza risultati
        successful_patches = [p for p in tested_candidates if p.test_results["success"]]
        failed_patches = [p for p in tested_candidates if not p.test_results["success"]]
        
        print(f"   Step 3: {len(successful_patches)} successful, {len(failed_patches)} failed")
        
        # Step 4: Verifica learning data
        for patch in tested_candidates:
            assert patch.test_results is not None
            assert "overall_score" in patch.test_results
            assert patch.success_probability >= 0.0
            
            # Dati per learning
            learning_data = {
                "error_pattern": error_analysis.error_pattern,
                "patch_strategy": patch.patch_strategy,
                "success": patch.test_results["success"],
                "score": patch.success_probability
            }
            
            print(f"   Learning data: {learning_data}")
        
        # Verifica che il ciclo sia completo
        assert len(tested_candidates) > 0
        assert all(p.test_results is not None for p in tested_candidates)
        assert all(p.success_probability >= 0.0 for p in tested_candidates)
        
        print("✅ Complete error → patch → test cycle: PASS")


def test_patch_testing_integration():
    """Test integrazione completa patch testing."""
    print("🧪 Test Integrazione Patch Testing...")
    
    # Test validation result
    test_class = TestPatchTesting()
    test_class.test_validation_result_criticality()
    test_class.test_validation_result_quality_threshold()
    print("✅ ValidationResult enhancements: PASS")
    
    # Test sandbox testing
    test_class.test_sandbox_patch_testing()
    print("✅ Sandbox patch testing: PASS")
    
    # Test patch generator
    test_class.test_patch_generator_with_testing()
    print("✅ PatchGenerator with testing: PASS")
    
    # Test success rate
    test_class.test_patch_success_rate_calculation()
    print("✅ Success rate calculation: PASS")
    
    # Test ciclo completo
    test_class.test_error_to_patch_to_test_cycle()
    print("✅ Complete error → patch → test cycle: PASS")
    
    print("\n🎉 Tutti i test Patch Testing: PASS")
    print("🚀 FASE 2.2 - Patch Testing & Validation: COMPLETATA")


if __name__ == "__main__":
    test_patch_testing_integration()
