"""
Test per Symbolic Reasoning - Integrazione SymbolMapper + NGReasoner
"""

import pytest
import sys
import os

# Aggiungi path per import
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from neuroglyph.cognitive.reasoner import NGReasoner
from neuroglyph.cognitive.data_structures import ParsedPrompt, MemoryContext
from neuroglyph.cognitive.reasoning_structures import FactType


class TestSymbolicReasoning:
    """Test per reasoning simbolico con simboli NEUROGLYPH."""
    
    def test_reasoner_with_symbols(self):
        """Test NGReasoner con simboli NEUROGLYPH."""
        reasoner = NGReasoner(max_depth=3, max_paths=2)
        
        # Verifica che SymbolMapper sia inizializzato
        assert hasattr(reasoner, 'symbol_mapper')
        assert reasoner.symbol_mapper is not None
        
        print("✅ NGReasoner con SymbolMapper inizializzato")
    
    def test_symbolic_facts_extraction(self):
        """Test estrazione fatti simbolici."""
        reasoner = NGReasoner()
        
        # Crea prompt con simboli
        parsed_prompt = ParsedPrompt(
            original_text="def process_data(): for item in data: if item.valid: return item",
            tokens=["def", "process_data", "(", ")", ":", "for", "item", "in", "data", ":", "if", "item", ".", "valid", ":", "return", "item"],
            segments=[],
            intents=["coding", "function_definition"],
            symbols=["⟲", "⦟", "⌮"],  # Loop, Conditional, Function
            metadata={"complexity": "medium"}
        )
        
        # Test estrazione fatti simbolici
        symbolic_facts = reasoner._extract_symbolic_facts(parsed_prompt.symbols, parsed_prompt)
        
        # Verifica che siano stati creati fatti
        assert len(symbolic_facts) > 0
        print(f"   Fatti simbolici estratti: {len(symbolic_facts)}")
        
        # Verifica struttura fatti
        predicates_found = set()
        for fact in symbolic_facts:
            assert hasattr(fact, 'predicate')
            assert hasattr(fact, 'confidence')
            assert fact.confidence > 0
            predicates_found.add(fact.predicate)
            
            print(f"   - {fact.statement} (confidence: {fact.confidence:.2f})")
        
        # Verifica che ci siano predicati simbolici attesi
        expected_predicates = {"IsLoop", "IsConditional", "IsFunction"}
        found_expected = predicates_found.intersection(expected_predicates)
        assert len(found_expected) > 0, f"Expected predicates {expected_predicates}, found {predicates_found}"
        
        print(f"   Predicati trovati: {predicates_found}")
        print(f"   Predicati attesi trovati: {found_expected}")
    
    def test_end_to_end_symbolic_reasoning(self):
        """Test reasoning end-to-end con simboli."""
        reasoner = NGReasoner(max_depth=2, max_paths=3)
        
        # Crea prompt con codice che contiene simboli
        parsed_prompt = ParsedPrompt(
            original_text="for i in range(10): if i % 2 == 0: print(i)",
            tokens=["for", "i", "in", "range", "(", "10", ")", ":", "if", "i", "%", "2", "==", "0", ":", "print", "(", "i", ")"],
            segments=[],
            intents=["coding", "loop", "conditional"],
            symbols=["⟲", "⦟", "⨑"],  # Loop, Conditional, LogicalAnd
            metadata={"type": "iteration_with_filter"}
        )
        
        # Crea memory context
        memory_context = MemoryContext(
            symbols=[
                {"symbol": "⟲", "frequency": 5, "contexts": ["loops"]},
                {"symbol": "⦟", "frequency": 3, "contexts": ["conditionals"]}
            ],
            episodes=[],
            errors=[],
            patterns=[],
            examples=[
                {
                    "prompt": "for x in items: process(x)",
                    "response": "Simple iteration pattern",
                    "domain": "code",
                    "relevance": 0.8
                }
            ]
        )
        
        # Esegui reasoning
        graph = reasoner.reason(memory_context, parsed_prompt)
        
        # Verifica risultati
        assert graph is not None
        assert graph.total_facts > 0
        assert len(graph.all_facts) > 0
        
        print(f"   Reasoning completato:")
        print(f"   - Total facts: {graph.total_facts}")
        print(f"   - Total paths: {len(graph.paths)}")
        print(f"   - Total steps: {graph.total_steps}")
        
        # Verifica che ci siano fatti simbolici
        symbolic_facts = [f for f in graph.all_facts if f.metadata and f.metadata.get('source') == 'symbol_mapper']
        assert len(symbolic_facts) > 0, "No symbolic facts found in reasoning graph"
        
        print(f"   - Symbolic facts: {len(symbolic_facts)}")
        
        # Verifica predicati simbolici
        symbolic_predicates = {f.predicate for f in symbolic_facts}
        expected_symbolic = {"IsLoop", "IsConditional", "LogicalAnd"}
        found_symbolic = symbolic_predicates.intersection(expected_symbolic)
        
        print(f"   - Symbolic predicates: {symbolic_predicates}")
        print(f"   - Expected found: {found_symbolic}")
        
        assert len(found_symbolic) > 0, f"No expected symbolic predicates found. Got: {symbolic_predicates}"
    
    def test_symbolic_reasoning_chain(self):
        """Test catena di reasoning simbolico A→B→C."""
        reasoner = NGReasoner(max_depth=3, max_paths=2)
        
        # Prompt con pattern complesso
        parsed_prompt = ParsedPrompt(
            original_text="def calculate_sum(numbers): total = 0; for num in numbers: if num > 0: total += num; return total",
            tokens=["def", "calculate_sum", "numbers", "total", "0", "for", "num", "in", "numbers", "if", "num", "0", "total", "num", "return", "total"],
            segments=[],
            intents=["function_definition", "aggregation", "filtering"],
            symbols=["⌮", "⟲", "⦟", "∑"],  # Function, Loop, Conditional, Summation
            metadata={"pattern": "filter_and_aggregate"}
        )
        
        memory_context = MemoryContext(
            symbols=[],
            episodes=[],
            errors=[],
            patterns=[],
            examples=[]
        )
        
        # Esegui reasoning
        graph = reasoner.reason(memory_context, parsed_prompt)
        
        # Verifica catena di reasoning
        assert graph.total_facts >= 4  # Almeno un fatto per simbolo
        
        # Cerca pattern di inferenza simbolica
        symbolic_facts = [f for f in graph.all_facts if f.metadata and f.metadata.get('source') == 'symbol_mapper']
        
        # Verifica che tutti i simboli abbiano generato fatti
        symbols_in_facts = {f.metadata.get('symbol') for f in symbolic_facts if f.metadata}
        expected_symbols = {"⌮", "⟲", "⦟", "∑"}
        
        print(f"   Simboli nei fatti: {symbols_in_facts}")
        print(f"   Simboli attesi: {expected_symbols}")
        
        # Almeno 3 dei 4 simboli dovrebbero essere presenti
        intersection = symbols_in_facts.intersection(expected_symbols)
        assert len(intersection) >= 3, f"Expected at least 3 symbols, found {len(intersection)}: {intersection}"
        
        # Verifica che ci siano fatti derivati (regole) o fatti con predicati Rule_*
        rule_facts = [f for f in symbolic_facts if f.fact_type == FactType.DERIVED or f.predicate.startswith('Rule_')]
        assert len(rule_facts) > 0, f"No derived facts from symbolic rules. Symbolic facts: {[f.predicate for f in symbolic_facts]}"
        
        print(f"   ✅ Catena simbolica A→B→C verificata")
        print(f"   - Simboli processati: {len(intersection)}/{len(expected_symbols)}")
        print(f"   - Fatti derivati: {len(rule_facts)}")
    
    def test_symbolic_reasoning_performance(self):
        """Test performance reasoning simbolico."""
        import time
        
        reasoner = NGReasoner(max_depth=2, max_paths=2)
        
        # Prompt con molti simboli
        parsed_prompt = ParsedPrompt(
            original_text="Complex code with multiple symbols",
            tokens=["complex", "code", "with", "multiple", "symbols"],
            segments=[],
            intents=["coding"],
            symbols=["⟲", "⦟", "⌮", "⨑", "⨒", "∑", "≡", "⧖"],  # 8 simboli
            metadata={}
        )
        
        memory_context = MemoryContext(symbols=[], episodes=[], errors=[], patterns=[], examples=[])
        
        # Misura tempo
        start_time = time.time()
        graph = reasoner.reason(memory_context, parsed_prompt)
        reasoning_time = time.time() - start_time
        
        # Verifica performance
        assert reasoning_time < 1.0, f"Reasoning too slow: {reasoning_time:.3f}s"
        assert graph.total_facts > 0
        
        print(f"   ✅ Performance test passed")
        print(f"   - Reasoning time: {reasoning_time:.3f}s")
        print(f"   - Facts generated: {graph.total_facts}")
        print(f"   - Symbols processed: {len(parsed_prompt.symbols)}")


def test_symbolic_reasoning_integration():
    """Test integrazione completa symbolic reasoning."""
    print("🧪 Test Integrazione Symbolic Reasoning...")
    
    # Test inizializzazione
    test_class = TestSymbolicReasoning()
    test_class.test_reasoner_with_symbols()
    print("✅ Reasoner initialization: PASS")
    
    # Test estrazione fatti simbolici
    test_class.test_symbolic_facts_extraction()
    print("✅ Symbolic facts extraction: PASS")
    
    # Test reasoning end-to-end
    test_class.test_end_to_end_symbolic_reasoning()
    print("✅ End-to-end symbolic reasoning: PASS")
    
    # Test catena reasoning
    test_class.test_symbolic_reasoning_chain()
    print("✅ Symbolic reasoning chain A→B→C: PASS")
    
    # Test performance
    test_class.test_symbolic_reasoning_performance()
    print("✅ Performance test: PASS")
    
    print("\n🎉 Tutti i test Symbolic Reasoning: PASS")
    print("🚀 FASE 2.1 - Reasoning Simbolico Reale: COMPLETATA")


if __name__ == "__main__":
    test_symbolic_reasoning_integration()
