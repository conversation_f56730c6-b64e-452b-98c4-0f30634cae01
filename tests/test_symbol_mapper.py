"""
Test per SymbolMapper - Reasoning Simbolico NEUROGLYPH
"""

import pytest
import sys
import os

# Aggiungi path per import
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from neuroglyph.cognitive.symbolic_reasoning.symbol_mapper import (
    SymbolMapper, SymbolMapping, SymbolCategory
)


class TestSymbolMapping:
    """Test per SymbolMapping dataclass."""
    
    def test_symbol_mapping_creation(self):
        """Test creazione SymbolMapping."""
        mapping = SymbolMapping(
            symbol="⟲",
            category=SymbolCategory.CONTROL_FLOW,
            semantic_predicate="IsLoop",
            logical_rules=["IsLoop(X) ⇒ CanIterate(X)"],
            inference_patterns=["loop_detection"]
        )
        
        assert mapping.symbol == "⟲"
        assert mapping.category == SymbolCategory.CONTROL_FLOW
        assert mapping.semantic_predicate == "IsLoop"
        assert len(mapping.logical_rules) == 1
        assert "loop_detection" in mapping.inference_patterns
        assert mapping.confidence == 1.0
        assert mapping.usage_count == 0


class TestSymbolMapper:
    """Test per SymbolMapper."""
    
    def test_symbol_mapper_initialization(self):
        """Test inizializzazione SymbolMapper."""
        mapper = SymbolMapper()
        
        # Verifica inizializzazione base
        assert isinstance(mapper.symbol_mappings, dict)
        assert isinstance(mapper.category_index, dict)
        assert isinstance(mapper.predicate_index, dict)
        
        # Verifica che ci siano mappings base
        assert len(mapper.symbol_mappings) > 0
        print(f"Simboli mappati: {len(mapper.symbol_mappings)}")
    
    def test_base_symbol_mappings(self):
        """Test mappings simboli base."""
        mapper = SymbolMapper()
        
        # Test simboli chiave
        test_cases = [
            ("⟲", "IsLoop", SymbolCategory.CONTROL_FLOW),
            ("⦟", "IsConditional", SymbolCategory.CONTROL_FLOW),
            ("⌮", "IsFunction", SymbolCategory.DATA_STRUCTURE),
            ("⨑", "LogicalAnd", SymbolCategory.LOGICAL_OPERATOR),
            ("≡", "Equivalent", SymbolCategory.RELATIONAL)
        ]
        
        for symbol, expected_predicate, expected_category in test_cases:
            mapping = mapper.get_mapping(symbol)
            assert mapping is not None, f"Mapping mancante per {symbol}"
            assert mapping.semantic_predicate == expected_predicate
            assert mapping.category == expected_category
            
            # Test metodi di accesso
            assert mapper.get_predicate(symbol) == expected_predicate
            assert len(mapper.get_logical_rules(symbol)) > 0
            assert len(mapper.get_inference_patterns(symbol)) > 0
    
    def test_category_indexing(self):
        """Test indicizzazione per categoria."""
        mapper = SymbolMapper()
        
        # Verifica che ci siano simboli per ogni categoria
        control_flow_symbols = mapper.symbols_by_category(SymbolCategory.CONTROL_FLOW)
        assert len(control_flow_symbols) >= 2  # ⟲, ⦟
        assert "⟲" in control_flow_symbols
        assert "⦟" in control_flow_symbols
        
        logical_symbols = mapper.symbols_by_category(SymbolCategory.LOGICAL_OPERATOR)
        assert len(logical_symbols) >= 1  # ⨑
        assert "⨑" in logical_symbols
    
    def test_predicate_indexing(self):
        """Test indicizzazione per predicato."""
        mapper = SymbolMapper()
        
        # Test ricerca per predicato
        loop_symbols = mapper.symbols_by_predicate("IsLoop")
        assert "⟲" in loop_symbols
        
        function_symbols = mapper.symbols_by_predicate("IsFunction")
        assert "⌮" in function_symbols
    
    def test_logical_facts_creation(self):
        """Test creazione fatti logici."""
        mapper = SymbolMapper()
        
        symbols = ["⟲", "⦟", "⌮"]
        context = {"code_type": "function", "complexity": "medium"}
        
        facts = mapper.create_logical_facts(symbols, context)
        
        # Verifica che siano stati creati fatti
        assert len(facts) > 0
        
        # Verifica struttura fatti
        for fact in facts:
            assert "predicate" in fact
            assert "symbol" in fact or "rule" in fact
            assert "confidence" in fact
        
        # Verifica che i simboli abbiano incrementato usage_count
        for symbol in symbols:
            mapping = mapper.get_mapping(symbol)
            if mapping:
                assert mapping.usage_count > 0
    
    def test_symbol_statistics(self):
        """Test statistiche simboli."""
        mapper = SymbolMapper()
        
        # Crea alcuni fatti per incrementare usage_count
        symbols = ["⟲", "⦟"]
        mapper.create_logical_facts(symbols, {})
        
        stats = mapper.get_symbol_statistics()
        
        # Verifica struttura statistiche
        assert "total_symbols" in stats
        assert "category_distribution" in stats
        assert "most_used_symbols" in stats
        assert "mapped_percentage" in stats
        
        assert stats["total_symbols"] > 0
        assert len(stats["category_distribution"]) > 0
        
        print(f"Statistiche: {stats}")
    
    def test_add_custom_mapping(self):
        """Test aggiunta mapping personalizzato."""
        mapper = SymbolMapper()
        
        # Crea mapping personalizzato
        custom_mapping = SymbolMapping(
            symbol="🔥",
            category=SymbolCategory.SEMANTIC,
            semantic_predicate="IsHot",
            logical_rules=["IsHot(X) ⇒ HighTemperature(X)"],
            inference_patterns=["temperature_inference"]
        )
        
        # Aggiungi mapping
        mapper.add_mapping(custom_mapping)
        
        # Verifica che sia stato aggiunto
        retrieved = mapper.get_mapping("🔥")
        assert retrieved is not None
        assert retrieved.semantic_predicate == "IsHot"
        
        # Verifica indicizzazione
        semantic_symbols = mapper.symbols_by_category(SymbolCategory.SEMANTIC)
        assert "🔥" in semantic_symbols
        
        hot_symbols = mapper.symbols_by_predicate("IsHot")
        assert "🔥" in hot_symbols


def test_symbol_mapper_integration():
    """Test integrazione completa SymbolMapper."""
    mapper = SymbolMapper()
    
    # Simula scenario reale: codice con simboli
    code_symbols = ["⟲", "⦟", "⌮", "⨑"]
    context = {
        "file": "test.py",
        "function": "process_data",
        "lines": 42
    }
    
    # Crea fatti logici
    facts = mapper.create_logical_facts(code_symbols, context)
    
    # Verifica che ogni simbolo abbia generato fatti
    predicates_found = set()
    for fact in facts:
        if "predicate" in fact:
            predicates_found.add(fact["predicate"])
    
    expected_predicates = {"IsLoop", "IsConditional", "IsFunction", "LogicalAnd"}
    assert len(predicates_found.intersection(expected_predicates)) > 0
    
    # Verifica statistiche finali
    stats = mapper.get_symbol_statistics()
    assert stats["total_symbols"] >= len(code_symbols)
    
    print(f"✅ Test integrazione completato")
    print(f"   Fatti generati: {len(facts)}")
    print(f"   Predicati trovati: {predicates_found}")
    print(f"   Simboli totali: {stats['total_symbols']}")


if __name__ == "__main__":
    # Esegui test direttamente
    print("🧪 Esecuzione test SymbolMapper...")
    
    # Test base
    test_mapping = TestSymbolMapping()
    test_mapping.test_symbol_mapping_creation()
    print("✅ SymbolMapping creation: PASS")
    
    # Test mapper
    test_mapper = TestSymbolMapper()
    test_mapper.test_symbol_mapper_initialization()
    print("✅ SymbolMapper initialization: PASS")
    
    test_mapper.test_base_symbol_mappings()
    print("✅ Base symbol mappings: PASS")
    
    test_mapper.test_category_indexing()
    print("✅ Category indexing: PASS")
    
    test_mapper.test_logical_facts_creation()
    print("✅ Logical facts creation: PASS")
    
    test_mapper.test_symbol_statistics()
    print("✅ Symbol statistics: PASS")
    
    # Test integrazione
    test_symbol_mapper_integration()
    
    print("\n🎉 Tutti i test SymbolMapper: PASS")
