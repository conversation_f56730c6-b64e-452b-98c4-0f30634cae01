#!/usr/bin/env python3
"""
Test minimale per NEUROGLYPH
"""

import sys
from pathlib import Path

# Aggiungi il path del progetto
sys.path.insert(0, str(Path(__file__).parent))

def main():
    print("🧪 Test Minimale NEUROGLYPH")
    print("=" * 40)
    
    try:
        print("1. Import moduli...")
        from neuroglyph.cognitive import NGMemory
        from neuroglyph.cognitive.data_structures import PriorityVector, DomainType
        print("✅ Import riuscito")
        
        print("2. Inizializzazione memory...")
        memory = NGMemory()
        print("✅ Memory inizializzato")
        
        print("3. Creazione PriorityVector...")
        prio_vec = PriorityVector(
            urgency=0.5,
            risk=0.2,
            domain=DomainType.CODE,
            confidence=0.8
        )
        print("✅ PriorityVector creato")
        
        print("4. Test append...")
        record = memory.append(
            prompt="test prompt",
            response="test response",
            prio_vec=prio_vec,
            symbols=[],  # Nessun simbolo per ora
            metadata={"test": True}
        )
        print(f"✅ Record creato: {record.id}")
        
        print("\n🎉 Test minimale completato con successo!")
        return 0
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())
