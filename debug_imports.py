"""
Script diagnostico per identificare import circolari in NEUROGLYPH
"""

import sys
import pkgutil
import importlib
import traceback
import os

def debug_imports():
    """Debug import circolari nel modulo cognitive."""
    print("🔍 Debugging import circolari NEUROGLYPH...")
    
    # Aggiungi path
    sys.path.insert(0, os.getcwd())
    
    # Mo<PERSON><PERSON> da testare in ordine di dipendenza
    test_modules = [
        'validation_structures',
        'sandbox_structures', 
        'patcher_structures',
        'data_structures',
        'memory_models',
        'reasoning_structures',
        'sandbox',
        'patch_generator',
        'reasoner',
        'memory',
        'integration'
    ]
    
    print(f"Testing {len(test_modules)} core modules...")
    
    for module_name in test_modules:
        try:
            print(f"   Testing: neuroglyph.cognitive.{module_name}...", end="")
            module = importlib.import_module(f'neuroglyph.cognitive.{module_name}')
            print(" ✅ OK")
        except Exception as e:
            print(f" ❌ FAILED")
            print(f"      Error: {str(e)}")
            traceback.print_exc(limit=3)
            print()
    
    # Test import specifici problematici
    print("\n🎯 Testing specific problematic imports...")
    
    specific_tests = [
        ('ValidationResult', 'from neuroglyph.cognitive.validation_structures import ValidationResult'),
        ('PatchGenerator', 'from neuroglyph.cognitive.patch_generator import PatchGenerator'),
        ('NGSandbox', 'from neuroglyph.cognitive.sandbox import NGSandbox'),
        ('PatchCandidate', 'from neuroglyph.cognitive.patcher_structures import PatchCandidate'),
    ]
    
    for name, import_stmt in specific_tests:
        try:
            print(f"   Testing: {name}...", end="")
            exec(import_stmt)
            print(" ✅ OK")
        except Exception as e:
            print(f" ❌ FAILED")
            print(f"      Error: {str(e)}")
            print()

if __name__ == "__main__":
    debug_imports()
